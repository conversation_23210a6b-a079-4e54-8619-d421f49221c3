# 中古車販売 ― Nuxt 管理プラットフォーム

中古車販売データを一元管理できる Nuxt 製の管理プラットフォームです。スクレイピング機能と堅牢なデータベース構成を備えています。

## アーキテクチャ

* **フロントエンド**: Nuxt 3（UI コンポーネント自動インポート対応）
* **データベース**: CockroachDB（PostgreSQL 互換）
* **管理画面**: Directus（ローコードでデータ操作）
* **スクレイピング**: Playwright 製スクレイパー（出力形式切替可）
* **開発環境**: Docker コンテナ対応

## 機能概要

* 🚗 **カーセンサー対応**のマルチブランド中古車データ取得・管理
* 📊 **出力モード 2 種**: CSV と／または DB へ保存
* 🐘 **CockroachDB 連携**（Docker で簡単セットアップ）
* 🔄 **バッチ Upsert**（重複時はコンフリクト解決）
* 📈 **URL ベース重複排除**
* 🚫 **ブラックリスト**車種／モデル除外
* 🧪 **ヘルスチェック** & エラーハンドリング
* 📱 **Directus ダッシュボード**

## かんたんスタート

### 前提条件

* Docker / Docker Compose
* Node.js 22 以上（ローカル開発用）

### 🚀 開発環境セットアップ（推奨）

**開発効率を重視したハイブリッド構成**
- **Nuxtアプリ**: ローカル実行（高速なHot Module Replacement）
- **データベースサービス**: Docker実行（環境の一貫性）

1. **環境変数ファイルをコピー**

   ```bash
   cp .env.example .env
   ```

2. **環境変数を設定 (重要!)**

   `.env` ファイルで以下の値を本番用に変更してください：
   ```env
   # セキュリティ警告: 本番環境では必ず変更してください！
   # 安全なランダムキーを生成: openssl rand -hex 32
   DIRECTUS_KEY=replace-with-secure-32-character-key
   DIRECTUS_SECRET=replace-with-secure-64-character-secret
   DIRECTUS_ADMIN_EMAIL=<EMAIL>
   DIRECTUS_ADMIN_PASSWORD=admin123
   ```

3. **データベースサービスを開始**

   ```bash
   docker-compose up -d
   ```

   **Docker で実行されるサービス:**
   - CockroachDB（データベース）
   - Directus（管理インターフェース）
   - Redis（キャッシュ、Directus用）
   - DB初期化サービス（初回のみ）

4. **Nuxtアプリケーションをローカルで開始**

   ```bash
   npm install
   npm run dev
   ```

**💡 なぜこの構成なのか？**
- **高速開発**: Nuxtの再ビルド時間を節約（Dockerイメージ再構築不要）
- **即座の反映**: コード変更が瞬時にブラウザに反映
- **デバッグ効率**: ローカルログとDevToolsに直接アクセス
- **リソース効率**: CPUとメモリ使用量を最適化

5. **アクセス URL**

   * **メインアプリ**: [http://localhost:3000](http://localhost:3000)
   * **Directus 管理画面**: [http://localhost:8056](http://localhost:8056)  （`<EMAIL>` / `admin123`）
   * **CockroachDB Admin UI**: [http://localhost:8081](http://localhost:8081)
   * **Redis**: localhost:6379 (内部サービス、直接アクセス不要)

## 📚 外部開発者向け API ドキュメント

Bubble.io などノーコードツールで Directus API を使う場合はこちら：

* **[REST API ガイド (日本語)](./docs/REST-API-GUIDE-JA.md)** - REST API経由での中古車データアクセス完全ガイド
* **[認証ガイド (日本語)](./docs/AUTHENTICATION-GUIDE-JA.md)** - ユーザー管理と認証設定ガイド
* **[REST API Guide (English)](./docs/REST-API-GUIDE.md)** - Complete REST API guide (English)
* **[Authentication Guide (English)](./docs/AUTHENTICATION-GUIDE.md)** - Authentication & user management guide (English)
* **[English README](./README-EN.md)** - Complete English documentation

> **本番環境への切替**は `.env` の `NODE_ENV=production` を変更するだけ。

### ローカル開発

```bash
# 依存パッケージをインストール
npm install

# 環境変数を設定
cp .env.example .env

# 開発サーバー起動
npm run dev
```

## データベース管理

### 接続設定

`.env` に以下を設定してください。

```env
DB_HOST=localhost
DB_PORT=26257
DB_NAME=usedcarsales
DB_USER=root
DB_PASSWORD=
DB_SSL=false
```

### サンプルデータ生成

```bash
# 1 万件のテストデータ生成
npm run generate-test-data

# 任意件数（例：100 万件）
npm run generate-sample-data
```

### DB テスト

```bash
# DB ステータス確認
npm run check-db

# ストレステスト
npm run stress-test-db
```

## スクレイパー連携

### 実行例

```bash
# CarSensor スクレイパー（DB 出力）
OUTPUT_MODE=database node scraper/carsensor.js

# CSV 出力
OUTPUT_MODE=csv node scraper/carsensor.js

# 両方出力
OUTPUT_MODE=both node scraper/carsensor.js
```

### デフォルトスケジュール

初期設定では **隔週日曜 2:00 AM** に実行。環境変数で変更可。

### API から起動

```bash
curl -X POST http://localhost:3000/api/scraper/start \
  -H "Content-Type: application/json" \
  -d '{"outputMode":"database"}'
```

## プロジェクト構成

```
├── scraper/                 # スクレイピング関連
│   ├── lib/                 # 共通ユーティリティ
│   ├── database/            # DB 接続 & スキーマ
│   └── carsensor.js # 最新安定版スクレイパー
├── pages/                   # Nuxt ページ
├── components/              # Vue コンポーネント
├── composables/             # Nuxt コンポーザブル
├── scripts/                 # データ生成等ユーティリティ
├── server/                  # サーバーサイド API
└── directus/                # Directus 設定
```

## 高度な機能

### ブラックリスト管理

```bash
# 車種を除外
node scripts/blacklist.js add maker "メルセデスベンツ" "高価格帯のため除外"
# 一覧表示
node scripts/blacklist.js list
```

### パフォーマンス監視

```bash
# DB ヘルスチェック
node scripts/health-check.js
```

## 本番デプロイ

```bash
# 本番ビルド
npm run build

# 本番ビルドのプレビュー
npm run preview
```

## 開発メモ

* 初回起動時にスキーマとサンプルレコードを自動生成
* Directus でノーコード管理が可能
* スクレイパーの実行スケジュールは環境変数で変更可
* CockroachDB の管理 UI: [http://localhost:8081](http://localhost:8081)

