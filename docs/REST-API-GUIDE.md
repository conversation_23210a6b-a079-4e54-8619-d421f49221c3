# Directus REST API Guide for Bubble.io Developers

This guide helps no-code developers using Bubble.io to integrate with the Directus REST API for accessing used car data.

## Official Content API Documentation
- **API Overview**: [Getting Started with the API](https://directus.io/docs/getting-started/use-the-api)
- **Query Parameters**: [Query Parameters Guide](https://directus.io/docs/guides/connect/query-parameters)

## 🚀 Quick Start

### Base URL
Your Directus API is available at:
```
http://localhost:8055/items/
```

For production, replace `localhost:8055` with your actual Directus domain.

### Authentication
All API requests require authentication. See the [AUTHENTICATION-GUIDE.md](./AUTHENTICATION-GUIDE.md) for details on getting your access token.

Include your access token in the header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📊 Used Car Data Structure

The main collection is `used_cars` with the following fields:

### Available Fields
| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Unique identifier |
| `car_name` | String | Full car name |
| `detail` | Text | Car description/details |
| `maker` | String | Car manufacturer (e.g., "トヨタ", "ホンダ") |
| `model` | String | Car model |
| `model_year` | Date | Manufacturing year |
| `displacement` | Integer | Engine displacement in CC |
| `driving_system` | String | Drive type (2WD, 4WD, etc.) |
| `engine_type` | String | Engine type (ガソリン, ディーゼル, ハイブリッド, etc.) |
| `mission` | String | Transmission details |
| `mission_type` | String | Transmission type (MT, AT, CVT) |
| `handle` | String | Steering position (右, 左) |
| `fix_history` | String | Repair history (なし, あり, etc.) |
| `mileage` | Integer | Mileage in kilometers |
| `vehicle_inspection` | String | Vehicle inspection info (車検) |
| `insurance` | String | Insurance information |
| `maintenance` | String | Maintenance information |
| `price` | Decimal | Price in 万円 (10,000 yen units) |
| `price_range` | String | General price range category |
| `price_range_500` | String | 500万円 bracket price range |
| `price_range_1000` | String | 1000万円 bracket price range |
| `region` | String | Location/region |
| `source` | String | Data source (e.g., "カーセンサー") |
| `source_url` | Text | Original listing URL |
| `image` | Text | Image URL(s) |
| `safe_equipment` | JSONB | Safety equipment list |
| `comfort_equipment` | JSONB | Comfort features list |
| `interior_equipment` | JSONB | Interior equipment list |
| `exterior_equipment` | JSONB | Exterior equipment list |
| `created_at` | DateTime | Record creation time |
| `updated_at` | DateTime | Last update time |
| `scraped_at` | DateTime | When data was collected |

## 🔍 Basic API Calls

### 1. Get All Used Cars
```http
GET http://localhost:8055/items/used_cars
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Bubble.io Setup:**
- API Connector: GET request
- URL: `http://localhost:8055/items/used_cars`
- Headers: `Authorization: Bearer YOUR_ACCESS_TOKEN`

### 2. Get Single Car by ID
```http
GET http://localhost:8055/items/used_cars/SOME_UUID_HERE
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 3. Get Limited Results (Pagination)
```http
GET http://localhost:8055/items/used_cars?limit=50&offset=0
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Parameters:**
- `limit`: Number of results (max 100)
- `offset`: Skip this many results (for pagination)

## 🎯 Filtering Examples

### Filter by Maker (Toyota cars only)
```http
GET http://localhost:8055/items/used_cars?filter[maker][_eq]=トヨタ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Price Range (2-5 million yen)
```http
GET http://localhost:8055/items/used_cars?filter[price][_gte]=200&filter[price][_lte]=500
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Multiple Makers
```http
GET http://localhost:8055/items/used_cars?filter[maker][_in]=トヨタ,ホンダ,日産
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Region
```http
GET http://localhost:8055/items/used_cars?filter[region][_contains]=東京
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Mileage (Under 50,000 km)
```http
GET http://localhost:8055/items/used_cars?filter[mileage][_lt]=50000
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Engine Type (Hybrid cars only)
```http
GET http://localhost:8055/items/used_cars?filter[engine_type][_eq]=ハイブリッド
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Transmission Type (Automatic only)
```http
GET http://localhost:8055/items/used_cars?filter[mission_type][_eq]=AT/CVT
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Driving System (4WD only)
```http
GET http://localhost:8055/items/used_cars?filter[driving_system][_eq]=4WD
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Repair History (No accidents)
```http
GET http://localhost:8055/items/used_cars?filter[fix_history][_eq]=なし
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Model Year (2020 or newer)
```http
GET http://localhost:8055/items/used_cars?filter[model_year][_gte]=2020-01-01
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Price Range Category
```http
GET http://localhost:8055/items/used_cars?filter[price_range][_eq]=201~300万円
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Equipment (Cars with navigation)
```http
GET http://localhost:8055/items/used_cars?filter[comfort_equipment][_contains]=カーナビ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Complex Filter (Toyota Hybrid, under 3M yen, Tokyo area, no accidents)
```http
GET http://localhost:8055/items/used_cars?filter[maker][_eq]=トヨタ&filter[engine_type][_eq]=ハイブリッド&filter[price][_lt]=300&filter[region][_contains]=東京&filter[fix_history][_eq]=なし
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📋 Sorting and Ordering

### Sort by Price (Ascending)
```http
GET http://localhost:8055/items/used_cars?sort=price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Sort by Price (Descending)
```http
GET http://localhost:8055/items/used_cars?sort=-price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Sort by Multiple Fields
```http
GET http://localhost:8055/items/used_cars?sort=maker,price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🎨 Field Selection

### Get Only Specific Fields
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,maker,model,price,mileage,engine_type,region
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Get Basic Car Info Only
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,maker,model,model_year,price,mileage,region,image
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Get Technical Specifications
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,displacement,engine_type,mission_type,driving_system,handle
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Get Equipment Information
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,safe_equipment,comfort_equipment,interior_equipment,exterior_equipment
Authorization: Bearer YOUR_ACCESS_TOKEN
```

This reduces response size and improves performance.

## 🔍 Search Functionality

### Search in Car Names
```http
GET http://localhost:8055/items/used_cars?filter[car_name][_contains]=プリウス
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Global Search (searches across multiple fields)
```http
GET http://localhost:8055/items/used_cars?search=プリウス
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🛠️ Working with Equipment Data (JSON Fields)

The equipment fields (`safe_equipment`, `comfort_equipment`, `interior_equipment`, `exterior_equipment`) contain JSON arrays of features.

### Example Equipment Data Structure
```json
{
  "safe_equipment": ["パワステ", "ABS", "エアバッグ：運転席/助手席", "横滑り防止装置"],
  "comfort_equipment": ["エアコン", "パワーウィンドウ", "集中ドアロック", "カーナビ"],
  "interior_equipment": ["本革シート", "シートヒーター", "電動シート"],
  "exterior_equipment": ["アルミホイール", "フォグランプ", "ルーフレール"]
}
```

### Filter by Specific Equipment
```http
GET http://localhost:8055/items/used_cars?filter[safe_equipment][_contains]=ABS
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Filter by Multiple Equipment Features
```http
GET http://localhost:8055/items/used_cars?filter[comfort_equipment][_contains]=カーナビ&filter[safe_equipment][_contains]=エアバッグ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Cars with Leather Seats
```http
GET http://localhost:8055/items/used_cars?filter[interior_equipment][_contains]=本革シート
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Cars with Alloy Wheels
```http
GET http://localhost:8055/items/used_cars?filter[exterior_equipment][_contains]=アルミホイール
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📊 Aggregation and Statistics

### Count Total Cars
```http
GET http://localhost:8055/items/used_cars?aggregate[count]=*
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Average Price
```http
GET http://localhost:8055/items/used_cars?aggregate[avg]=price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### Group by Maker with Count
```http
GET http://localhost:8055/items/used_cars?groupBy[]=maker&aggregate[count]=*
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🛠️ Bubble.io Integration Tips

### 1. Setting up API Connector
1. Go to Plugins → API Connector
2. Add new API: "Directus Used Cars"
3. Base URL: `http://localhost:8055/items/`
4. Add shared headers: `Authorization: Bearer YOUR_TOKEN`

### 2. Creating Calls
For each endpoint, create a new call:
- **Name**: "Get Used Cars"
- **Use as**: Data (JSON)
- **Data type**: JSON
- **URL**: `used_cars`

### 3. Dynamic Filters in Bubble
Use URL parameters to make filters dynamic:
```
used_cars?filter[maker][_eq]=<maker>&filter[price][_lte]=<max_price>
```

### 4. Handling Responses
Directus returns data in this format:
```json
{
  "data": [
    {
      "id": "uuid-here",
      "car_name": "プリウス S",
      "maker": "トヨタ",
      "price": 250,
      ...
    }
  ]
}
```

Access the car list using: `Result of API call's data`

## 📚 Common Filter Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `_eq` | Equals | `filter[maker][_eq]=トヨタ` |
| `_neq` | Not equals | `filter[maker][_neq]=トヨタ` |
| `_lt` | Less than | `filter[price][_lt]=300` |
| `_lte` | Less than or equal | `filter[price][_lte]=300` |
| `_gt` | Greater than | `filter[mileage][_gt]=10000` |
| `_gte` | Greater than or equal | `filter[mileage][_gte]=10000` |
| `_in` | In list | `filter[maker][_in]=トヨタ,ホンダ` |
| `_nin` | Not in list | `filter[maker][_nin]=高級車` |
| `_contains` | Contains text | `filter[car_name][_contains]=プリウス` |
| `_ncontains` | Doesn't contain | `filter[car_name][_ncontains]=事故` |
| `_null` | Is null | `filter[detail][_null]=true` |
| `_nnull` | Is not null | `filter[detail][_nnull]=true` |

## 🚨 Error Handling

Common HTTP status codes:
- `200`: Success
- `401`: Unauthorized (check your token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found
- `500`: Server error

## 📖 Additional Resources

- [Official Directus API Documentation](https://docs.directus.io/reference/introduction/)
- [Directus Filter Guide](https://docs.directus.io/reference/filter-rules/)
- [Directus Query Parameters](https://docs.directus.io/reference/query/)
- [Authentication Guide](./AUTHENTICATION-GUIDE.md)

## 💡 Performance Tips

1. **Use field selection** to reduce response size
2. **Implement pagination** for large datasets
3. **Cache responses** in Bubble when possible
4. **Use specific filters** instead of global search when possible
5. **Limit results** to what you actually need

## 🔧 Troubleshooting

### CORS Issues
If you get CORS errors, ensure Directus is configured to allow your domain. Contact your administrator to add your Bubble.io preview/live domains to the CORS whitelist.
