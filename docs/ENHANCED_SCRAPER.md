# Enhanced Scraper Documentation

The scraper has been upgraded with robust retry logic, email alerting, and failure handling to make the scraping process more reliable and observable. The enhanced features are now built into the main `carsensor.js` scraper.

## 🚀 Key Features

### **Retry Logic with Exponential Backoff**
- **3-tier retry system**: Brand-level, page-level, and detail-level retries
- **Exponential backoff**: Delays increase exponentially (e.g., 5s → 15s → 45s)
- **Jitter**: Random delay variation to prevent thundering herd
- **Smart error classification**: Distinguishes retryable vs permanent errors

### **Email Alerting System**  
- **Optional SMTP support**: Works with any SMTP provider (Resend, Gmail, etc.)
- **Graceful degradation**: Runs without email if SMTP not available
- **Alert types**: Total failures, critical errors, brand failures, success summaries
- **Rate limiting**: Configurable cooldown to prevent spam
- **Rich HTML emails**: Formatted alerts with error details

### **Failure Recovery & Progress Tracking**
- **Smart skipping**: Continue with other brands when one fails
- **Progress persistence**: Resume where you left off after interruptions
- **Graceful degradation**: System continues even with partial failures
- **Detailed logging**: Structured error reporting and metrics

## 📋 Configuration

### Dependencies

The scraper automatically works without email dependencies. To enable email notifications, install the optional SMTP dependency:

```bash
# For email notifications (optional)
npm install nodemailer
```

### Environment Variables

Copy `.env.scraper.example` to `.env` and configure:

```bash
# Retry Configuration
SCRAPER_BRAND_MAX_ATTEMPTS=3           # Brand-level retry attempts
SCRAPER_PAGE_MAX_ATTEMPTS=3            # Page-level retry attempts  
SCRAPER_DETAIL_MAX_ATTEMPTS=3          # Detail-level retry attempts
SCRAPER_BRAND_BASE_DELAY_MS=5000       # Base delay for brand retries (5s)
SCRAPER_PAGE_BASE_DELAY_MS=2000        # Base delay for page retries (2s)
SCRAPER_DETAIL_BASE_DELAY_MS=1000      # Base delay for detail retries (1s)
SCRAPER_BACKOFF_MULTIPLIER=3           # Exponential backoff multiplier

# Email Configuration
EMAIL_NOTIFICATIONS_ENABLED=true
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=resend
SMTP_PASS=your_api_key
FROM_EMAIL=<EMAIL>
TO_EMAILS=<EMAIL>,<EMAIL>
EMAIL_ALERT_COOLDOWN_MINUTES=30

# Behavior Configuration  
MAX_BRANDS_TO_PROCESS=0                # 0 = process all brands
SKIP_FAILED_BRANDS=true               # Skip previously failed brands
SAVE_PROGRESS_FILE=true               # Enable progress persistence
```

## 🔧 Usage

### Running the Scraper

```bash
# From scraper directory  
npm start

# With specific output mode
npm run scrape:csv
npm run scrape:db

# Or directly
node carsensor.js
```

### From Nuxt Application

The scraper runs with all enhanced features by default:

```typescript
import { runScraper } from '~/server/utils/scraper-service'

// Run scraper with enhanced features
await runScraper('both', 'manual')
```

## 📊 Retry Logic Details

### **Brand-Level Retries**
- **Triggers**: Network timeouts, browser crashes, page load failures
- **Default**: 3 attempts with 5s → 15s → 45s delays  
- **Behavior**: If brand fails completely, skip to next brand

### **Page-Level Retries**  
- **Triggers**: Page navigation failures, selector timeouts
- **Default**: 3 attempts with 2s → 6s → 18s delays
- **Behavior**: Retry current page within brand processing

### **Detail-Level Retries**
- **Triggers**: Individual car detail page failures  
- **Default**: 3 attempts with 1s → 3s → 9s delays
- **Behavior**: Skip individual car if all retries fail

### **Error Classification**

**Retryable Errors:**
- Network timeouts (ETIMEDOUT, ECONNRESET)
- HTTP 5xx errors (502, 503, 504)
- Rate limiting (429)
- Temporary failures

**Critical Errors (no retry):**
- Browser crashes
- Authentication failures
- Disk full errors
- Permission denied

## 📧 Email Alert Types

### **Total Failure Alert** 🚨
Sent when a brand fails after all retry attempts are exhausted.

### **Critical Error Alert** 🔥  
Sent for system-level errors that require immediate attention.

### **Brand Failure Alert** ⚠️
Sent during retry attempts to track intermediate failures.

### **Success Summary** ✅
Daily summary of completed scraper runs with statistics.

### **Alert Cooldown**
Prevents email spam by enforcing cooldown periods between similar alerts.

## 📈 Progress Tracking

The enhanced scraper maintains progress state in `scraper_progress.json`:

```json
{
  "processedBrands": ["Toyota", "Honda", "Nissan"],
  "failedBrands": ["SomeBrand"],
  "totalCarsScraped": 15420,
  "startTime": "2024-01-15T10:00:00.000Z",
  "lastSavedAt": "2024-01-15T12:30:00.000Z"
}
```

**Benefits:**
- Resume interrupted scraping sessions
- Skip already processed brands  
- Track cumulative statistics
- Monitor scraping progress

## 🔄 Backup & Recovery

### **Graceful Interruption**
- Progress saved every 5 brands processed
- Auto-save every 5 minutes during execution
- Final save on completion or error

### **Recovery Process**
1. Scraper loads existing progress file on startup
2. Skips already processed brands (if `SKIP_FAILED_BRANDS=true`)
3. Continues from where it left off
4. Maintains cumulative statistics

### **Manual Recovery**
```bash
# View current progress
cat scraper/scraper_progress.json

# Reset progress (start from beginning)  
rm scraper/scraper_progress.json

# Process only failed brands
# Set SKIP_FAILED_BRANDS=false and manually edit progress file
```

## 🧪 Testing

### **Limited Brand Testing**
```bash
# Test with only first 5 brands
MAX_BRANDS_TO_PROCESS=5 npm start
```

### **Email Testing**
```bash
# Enable email for testing
EMAIL_NOTIFICATIONS_ENABLED=true 
EMAIL_ALERT_COOLDOWN_MINUTES=1  # Reduce cooldown for testing
npm start
```

### **Retry Testing**
```bash
# Aggressive retry testing
SCRAPER_BRAND_MAX_ATTEMPTS=1
SCRAPER_PAGE_MAX_ATTEMPTS=1  
SCRAPER_DETAIL_MAX_ATTEMPTS=1
npm start
```

## 📝 Monitoring & Logs

### **Structured Logging**
- Brand processing progress
- Retry attempt details  
- Error classification
- Performance metrics

### **Progress Indicators**
- `🚀` Starting brand processing
- `📄` Page processing complete
- `🔄` Retry attempts
- `✅` Brand completion
- `❌` Brand failure
- `⏭️` Brand skipped (zero results)
- `💾` Database save confirmation

### **Email Integration**
- Real-time failure notifications
- Daily summary reports
- Alert rate limiting
- HTML formatted emails

## 🔧 Troubleshooting

### **Common Issues**

**Email not sending:**
- Check SMTP credentials
- Verify FROM_EMAIL domain authorization
- Check firewall/network restrictions
- Review email logs in console

**High retry failures:**
- Reduce retry attempts for testing
- Check network stability
- Monitor target website availability
- Adjust delay timings

**Progress file corruption:**
- Delete `scraper_progress.json` to reset
- Check disk space and permissions
- Review JSON syntax if manually edited

**Memory issues:**
- Limit brands with `MAX_BRANDS_TO_PROCESS`  
- Monitor browser memory usage
- Consider restarting for very long runs

### **Performance Tuning**

**Reduce delays for faster processing:**
```bash
SCRAPER_BRAND_BASE_DELAY_MS=1000
SCRAPER_PAGE_BASE_DELAY_MS=500
SCRAPER_DETAIL_BASE_DELAY_MS=200
```

**Increase delays for better reliability:**
```bash
SCRAPER_BRAND_BASE_DELAY_MS=10000
SCRAPER_PAGE_BASE_DELAY_MS=5000  
SCRAPER_DETAIL_BASE_DELAY_MS=2000
```

## 🔐 Security Considerations

- Store SMTP credentials securely
- Use environment variables, not hardcoded values
- Restrict email recipient lists
- Monitor for credential leakage in logs
- Use app-specific passwords for Gmail
- Consider SMTP over TLS (port 465) for sensitive environments

## 🚀 Getting Started

1. **Install email dependencies** (optional): `npm install nodemailer`
2. **Copy configuration** from `.env.scraper.example` to `.env`
3. **Test with limited brands** first: `MAX_BRANDS_TO_PROCESS=2 npm start`
4. **Monitor output** and configure email alerts if desired
5. **Run full scraper** when ready: `npm start`