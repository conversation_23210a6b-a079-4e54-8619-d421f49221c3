# Directus Authentication Guide for Bubble.io Developers

This guide explains how to authenticate with <PERSON><PERSON> and manage users for your Bubble.io application.

## Official Authentication Documentation
- **Authentication Guide**: [Directus Authentication](https://directus.io/docs/guides/connect/authentication/)

## 🚀 Quick Start

### Base URLs
- **Admin Panel**: http://localhost:8055
- **API Base**: http://localhost:8055
- **Auth Endpoint**: http://localhost:8055/auth

For production, replace `localhost:8055` with your actual Directus domain.

## 👤 Admin Access

### Default Admin Credentials
```
Email: <EMAIL>
Password: your-secure-password-here
```

*Note: These are set in your `.env` file under `DIRECTUS_ADMIN_EMAIL` and `DIRECTUS_ADMIN_PASSWORD`*

### Accessing Admin Panel
1. Go to http://localhost:8055
2. Login with admin credentials
3. Navigate to User Directory to manage users

## 🔐 Authentication Methods

### Method 1: Static Token (Recommended for Bubble.io)

#### Creating a Static Token
1. Login to Directus Admin Panel
2. Go to **Settings** → **Access Tokens**
3. Click **Create Token**
4. Set:
   - **Name**: "Bubble.io App Token"
   - **Token**: Leave blank (auto-generated) or set custom
   - **Role**: Choose appropriate role (see Roles section)
   - **Expires**: Set expiration or leave blank for no expiry

#### Using Static Token in Bubble.io
Add this header to all API calls:
```
Authorization: Bearer YOUR_STATIC_TOKEN_HERE
```

**Bubble.io Setup:**
- API Connector → Shared Headers
- Key: `Authorization`
- Value: `Bearer YOUR_STATIC_TOKEN_HERE`

### Method 2: Login-based Authentication

#### Login API Call
```http
POST http://localhost:8055/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Response:**
```json
{
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires": 900000,
    "refresh_token": "abcdef123456..."
  }
}
```

#### Using Access Token
Use the `access_token` from login response:
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 👥 User Management

### Creating Users via Admin Panel

1. **Login to Admin Panel** (http://localhost:8055)
2. **Go to User Directory** → **Users**
3. **Click Create User**
4. **Fill in details:**
   - **First Name**: User's first name
   - **Last Name**: User's last name  
   - **Email**: <EMAIL>
   - **Password**: Set secure password
   - **Role**: Assign appropriate role
   - **Status**: Active

### Creating Users via API

```http
POST http://localhost:8055/users
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "role": "ROLE_UUID_HERE",
  "status": "active"
}
```

### User Roles and Permissions

#### Default Roles
1. **Administrator**: Full access to everything
2. **Public**: Limited read-only access
3. **Custom Roles**: Create as needed

#### Creating Custom Roles
1. **Admin Panel** → **Settings** → **Roles & Permissions**
2. **Click Create Role**
3. **Set permissions** for each collection:
   - **Create**: Can add new records
   - **Read**: Can view records
   - **Update**: Can modify records
   - **Delete**: Can remove records

#### Recommended Role for Bubble.io Apps
Create a role called "App User" with:
- **used_cars**: Read access only
- **Other collections**: As needed
- **No admin panel access**

## 🔄 Token Management

### Token Refresh
Access tokens expire. Use refresh tokens to get new access tokens:

```http
POST http://localhost:8055/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

**Response:**
```json
{
  "data": {
    "access_token": "new_access_token_here",
    "expires": 900000,
    "refresh_token": "new_refresh_token_here"
  }
}
```

### Logout
```http
POST http://localhost:8055/auth/logout
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

## 🛡️ Security Best Practices

### For Production
1. **Change default admin password**
2. **Use HTTPS only**
3. **Set token expiration times**
4. **Create role-specific users**
5. **Enable CORS for your domains only**
6. **Use environment variables for secrets**

### For Development
1. **Use static tokens for simplicity**
2. **Create test users with limited permissions**
3. **Don't commit tokens to version control**

## 🔧 Bubble.io Integration Examples

### Example 1: Static Token Setup
**API Connector Configuration:**
- **API Name**: Directus Cars API
- **Server URL**: http://localhost:8055/items/
- **Shared Headers**:
  - `Authorization`: `Bearer your_static_token_here`
  - `Content-Type`: `application/json`

### Example 2: Login Flow
**Step 1 - Login Call:**
- **Name**: "Directus Login"
- **Use as**: Action
- **Method**: POST
- **URL**: `http://localhost:8055/auth/login`
- **Body**: 
```json
{
  "email": "<email>",
  "password": "<password>"
}
```

**Step 2 - Store Token:**
After successful login, store `Result of API call's data access_token` in a custom state or database.

**Step 3 - Use Token:**
For subsequent API calls, use dynamic headers:
- `Authorization`: `Bearer [stored_token]`

### Example 3: User Registration
```http
POST http://localhost:8055/users
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "first_name": "<first_name>",
  "last_name": "<last_name>", 
  "email": "<email>",
  "password": "<password>",
  "role": "app_user_role_uuid"
}
```

## 🚨 Common Issues & Solutions

### Issue: 401 Unauthorized
**Causes:**
- Invalid or expired token
- Missing Authorization header
- Wrong token format

**Solutions:**
- Check token is correct
- Ensure header format: `Bearer TOKEN`
- Refresh expired tokens

### Issue: 403 Forbidden  
**Causes:**
- User lacks permissions for the action
- Role doesn't have access to collection

**Solutions:**
- Check user role permissions
- Assign appropriate role
- Contact admin for permission changes

### Issue: CORS Errors
**Causes:**
- Directus not configured for your domain
- Missing CORS headers

**Solutions:**
- Add your domain to Directus CORS settings
- Contact administrator to whitelist your domain

### Issue: Token Not Working
**Troubleshooting:**
1. Verify token in Directus admin panel
2. Check token hasn't expired
3. Ensure role has proper permissions
4. Test with Postman/curl first

## 📊 Testing Authentication

### Test with curl
```bash
# Test login
curl -X POST http://localhost:8055/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test API access
curl -X GET http://localhost:8055/items/used_cars \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test in Bubble.io
1. **Create test API call** with your token
2. **Use API Connector debugger** to test
3. **Check response** for proper data
4. **Verify permissions** work as expected

## 📚 Additional Resources

- [Official Directus Authentication Docs](https://docs.directus.io/reference/authentication/)
- [Directus User Management](https://docs.directus.io/app/user-directory/)
- [Directus Roles & Permissions](https://docs.directus.io/app/user-directory/roles/)
- [JWT Token Guide](https://docs.directus.io/reference/authentication/#jwt-token)
- [API Access Tokens](https://docs.directus.io/reference/authentication/#access-tokens)

## 💡 Pro Tips

1. **Use static tokens** for server-to-server communication
2. **Use login flow** for user-facing applications  
3. **Set appropriate token expiration** times
4. **Create specific roles** for different access levels
5. **Test permissions** thoroughly before production
6. **Monitor token usage** in production
7. **Implement token refresh** for long-running applications

## 🔍 Debugging Authentication

### Check Token Validity
```http
GET http://localhost:8055/users/me
Authorization: Bearer YOUR_TOKEN
```

If this returns user info, your token is valid.

### Check Permissions
```http
GET http://localhost:8055/permissions/me
Authorization: Bearer YOUR_TOKEN
```

This shows what permissions your token has.

### Admin Panel Logs
Check the Directus admin panel logs for authentication errors and permission denials.
