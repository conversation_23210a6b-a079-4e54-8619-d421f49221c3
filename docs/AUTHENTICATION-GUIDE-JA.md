# Bubble.io開発者向けDirectus認証ガイド

このガイドでは、Directusでの認証方法とBubble.ioアプリケーション用のユーザー管理について説明します。

## 🚀 クイックスタート

### ベースURL
- **管理パネル**: http://localhost:8055
- **API ベース**: http://localhost:8055
- **認証エンドポイント**: http://localhost:8055/auth

本番環境では、`localhost:8055`を実際のDirectusドメインに置き換えてください。

## 公式認証文書
- **認証ガイド**: [Directus認証](https://directus.io/docs/guides/connect/authentication/)

## 👤 管理者アクセス

### デフォルト管理者認証情報
```
メール: <EMAIL>
パスワード: your-secure-password-here
```

*注意: これらは`.env`ファイルの`DIRECTUS_ADMIN_EMAIL`と`DIRECTUS_ADMIN_PASSWORD`で設定されています*

### 管理パネルへのアクセス
1. http://localhost:8055 にアクセス
2. 管理者認証情報でログイン
3. ユーザーディレクトリに移動してユーザーを管理

## 🔐 認証方法

### 方法1: 静的トークン（Bubble.io推奨）

#### 静的トークンの作成
1. Directus管理パネルにログイン
2. **設定** → **アクセストークン**に移動
3. **トークンを作成**をクリック
4. 設定：
   - **名前**: "Bubble.io App Token"
   - **トークン**: 空白のまま（自動生成）またはカスタム設定
   - **ロール**: 適切なロールを選択（ロールセクション参照）
   - **有効期限**: 有効期限を設定するか、無期限の場合は空白

#### Bubble.ioでの静的トークン使用
すべてのAPIコールにこのヘッダーを追加：
```
Authorization: Bearer YOUR_STATIC_TOKEN_HERE
```

**Bubble.io設定:**
- API Connector → 共有ヘッダー
- キー: `Authorization`
- 値: `Bearer YOUR_STATIC_TOKEN_HERE`

### 方法2: ログインベース認証

#### ログインAPIコール
```http
POST http://localhost:8055/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**レスポンス:**
```json
{
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires": 900000,
    "refresh_token": "abcdef123456..."
  }
}
```

#### アクセストークンの使用
ログインレスポンスの`access_token`を使用：
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 👥 ユーザー管理

### 管理パネル経由でのユーザー作成

1. **管理パネルにログイン** (http://localhost:8055)
2. **ユーザーディレクトリ** → **ユーザー**に移動
3. **ユーザーを作成**をクリック
4. **詳細を入力:**
   - **名**: ユーザーの名前
   - **姓**: ユーザーの姓
   - **メール**: <EMAIL>
   - **パスワード**: 安全なパスワードを設定
   - **ロール**: 適切なロールを割り当て
   - **ステータス**: アクティブ

### API経由でのユーザー作成

```http
POST http://localhost:8055/users
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "first_name": "太郎",
  "last_name": "田中",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "role": "ROLE_UUID_HERE",
  "status": "active"
}
```

### ユーザーロールと権限

#### デフォルトロール
1. **管理者**: すべてへの完全アクセス
2. **パブリック**: 限定的な読み取り専用アクセス
3. **カスタムロール**: 必要に応じて作成

#### カスタムロールの作成
1. **管理パネル** → **設定** → **ロールと権限**
2. **ロールを作成**をクリック
3. **各コレクションの権限を設定:**
   - **作成**: 新しいレコードを追加可能
   - **読み取り**: レコードを表示可能
   - **更新**: レコードを変更可能
   - **削除**: レコードを削除可能

#### Bubble.ioアプリ推奨ロール
「アプリユーザー」というロールを作成し、以下を設定：
- **used_cars**: 読み取りアクセスのみ
- **その他のコレクション**: 必要に応じて
- **管理パネルアクセスなし**

## 🔄 トークン管理

### トークン更新
アクセストークンは期限切れになります。リフレッシュトークンを使用して新しいアクセストークンを取得：

```http
POST http://localhost:8055/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

**レスポンス:**
```json
{
  "data": {
    "access_token": "new_access_token_here",
    "expires": 900000,
    "refresh_token": "new_refresh_token_here"
  }
}
```

### ログアウト
```http
POST http://localhost:8055/auth/logout
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

## 🛡️ セキュリティベストプラクティス

### 本番環境用
1. **デフォルト管理者パスワードを変更**
2. **HTTPS のみ使用**
3. **トークン有効期限を設定**
4. **ロール固有のユーザーを作成**
5. **あなたのドメインのみCORSを有効化**
6. **秘密情報には環境変数を使用**

### 開発環境用
1. **簡単にするため静的トークンを使用**
2. **限定権限のテストユーザーを作成**
3. **トークンをバージョン管理にコミットしない**

## 🔧 Bubble.io連携例

### 例1: 静的トークン設定
**API Connector設定:**
- **API名**: Directus Cars API
- **サーバーURL**: http://localhost:8055/items/
- **共有ヘッダー**:
  - `Authorization`: `Bearer your_static_token_here`
  - `Content-Type`: `application/json`

### 例2: ログインフロー
**ステップ1 - ログインコール:**
- **名前**: "Directus Login"
- **使用方法**: Action
- **メソッド**: POST
- **URL**: `http://localhost:8055/auth/login`
- **ボディ**: 
```json
{
  "email": "<email>",
  "password": "<password>"
}
```

**ステップ2 - トークンを保存:**
ログイン成功後、`Result of API call's data access_token`をカスタム状態またはデータベースに保存。

**ステップ3 - トークンを使用:**
後続のAPIコールでは、動的ヘッダーを使用：
- `Authorization`: `Bearer [stored_token]`

### 例3: ユーザー登録
```http
POST http://localhost:8055/users
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "first_name": "<first_name>",
  "last_name": "<last_name>", 
  "email": "<email>",
  "password": "<password>",
  "role": "app_user_role_uuid"
}
```

## 🚨 よくある問題と解決策

### 問題: 401 Unauthorized
**原因:**
- 無効または期限切れのトークン
- Authorizationヘッダーの欠如
- 間違ったトークン形式

**解決策:**
- トークンが正しいことを確認
- ヘッダー形式を確認: `Bearer TOKEN`
- 期限切れトークンを更新

### 問題: 403 Forbidden  
**原因:**
- ユーザーがアクションの権限を持たない
- ロールがコレクションへのアクセス権を持たない

**解決策:**
- ユーザーロール権限を確認
- 適切なロールを割り当て
- 権限変更について管理者に連絡

### 問題: CORSエラー
**原因:**
- Directusがあなたのドメイン用に設定されていない
- CORSヘッダーの欠如

**解決策:**
- DirectusのCORS設定にあなたのドメインを追加
- 管理者に連絡してドメインをホワイトリストに追加

### 問題: トークンが機能しない
**トラブルシューティング:**
1. Directus管理パネルでトークンを確認
2. トークンが期限切れでないことを確認
3. ロールが適切な権限を持つことを確認
4. 最初にPostman/curlでテスト

## 📊 認証のテスト

### curlでのテスト
```bash
# ログインテスト
curl -X POST http://localhost:8055/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# APIアクセステスト
curl -X GET http://localhost:8055/items/used_cars \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Bubble.ioでのテスト
1. **トークンでテストAPIコールを作成**
2. **API Connectorデバッガーを使用してテスト**
3. **適切なデータのレスポンスを確認**
4. **権限が期待通りに動作することを確認**

## 📚 追加リソース

- [公式Directus認証文書](https://docs.directus.io/reference/authentication/)
- [Directusユーザー管理](https://docs.directus.io/app/user-directory/)
- [Directusロールと権限](https://docs.directus.io/app/user-directory/roles/)
- [JWTトークンガイド](https://docs.directus.io/reference/authentication/#jwt-token)
- [APIアクセストークン](https://docs.directus.io/reference/authentication/#access-tokens)

## 💡 プロのコツ

1. **サーバー間通信には静的トークンを使用**
2. **ユーザー向けアプリケーションにはログインフローを使用**
3. **適切なトークン有効期限を設定**
4. **異なるアクセスレベル用に特定のロールを作成**
5. **本番前に権限を徹底的にテスト**
6. **本番でのトークン使用を監視**
7. **長時間実行アプリケーションにはトークン更新を実装**

## 🔍 認証のデバッグ

### トークンの有効性確認
```http
GET http://localhost:8055/users/me
Authorization: Bearer YOUR_TOKEN
```

これがユーザー情報を返す場合、トークンは有効です。

### 権限の確認
```http
GET http://localhost:8055/permissions/me
Authorization: Bearer YOUR_TOKEN
```

これはあなたのトークンが持つ権限を表示します。

### 管理パネルログ
認証エラーや権限拒否については、Directus管理パネルのログを確認してください。
