# Bubble.io開発者向けDirectus REST APIガイド

このガイドは、Bubble.ioを使用するノーコード開発者がDirectus REST APIと連携して中古車データにアクセスするためのものです。

## 🚀 クイックスタート

### ベースURL
Directus APIは以下のURLで利用できます：
```
http://localhost:8055/items/
```

本番環境では、`localhost:8055`を実際のDirectusドメインに置き換えてください。

## 公式コンテンツAPI文書
- **API概要**: [APIを使い始める](https://directus.io/docs/getting-started/use-the-api)
- **クエリパラメータ**: [クエリパラメータガイド](https://directus.io/docs/guides/connect/query-parameters)

### 認証
すべてのAPIリクエストには認証が必要です。アクセストークンの取得方法については、[AUTHENTICATION-GUIDE-JA.md](./AUTHENTICATION-GUIDE-JA.md)を参照してください。

ヘッダーにアクセストークンを含めてください：
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📊 中古車データ構造

メインコレクションは`used_cars`で、以下のフィールドがあります：

### 利用可能なフィールド
| フィールド | タイプ | 説明 |
|-------|------|-------------|
| `id` | UUID | 一意識別子 |
| `car_name` | String | 車名 |
| `detail` | Text | 車の詳細・説明 |
| `maker` | String | メーカー（例：「トヨタ」、「ホンダ」） |
| `model` | String | 車種 |
| `model_year` | Date | 年式 |
| `displacement` | Integer | 排気量（CC） |
| `driving_system` | String | 駆動方式（2WD、4WD等） |
| `engine_type` | String | エンジンタイプ（ガソリン、ディーゼル、ハイブリッド等） |
| `mission` | String | ミッション詳細 |
| `mission_type` | String | ミッションタイプ（MT、AT、CVT） |
| `handle` | String | ハンドル位置（右、左） |
| `fix_history` | String | 修復歴（なし、あり等） |
| `mileage` | Integer | 走行距離（km） |
| `vehicle_inspection` | String | 車検情報 |
| `insurance` | String | 保険情報 |
| `maintenance` | String | 整備情報 |
| `price` | Decimal | 価格（万円単位） |
| `price_range` | String | 価格帯カテゴリ |
| `price_range_500` | String | 500万円区切りの価格帯 |
| `price_range_1000` | String | 1000万円区切りの価格帯 |
| `region` | String | 地域 |
| `source` | String | データソース（例：「カーセンサー」） |
| `source_url` | Text | 元の掲載URL |
| `image` | Text | 画像URL |
| `safe_equipment` | JSONB | 安全装備リスト |
| `comfort_equipment` | JSONB | 快適装備リスト |
| `interior_equipment` | JSONB | 内装装備リスト |
| `exterior_equipment` | JSONB | 外装装備リスト |
| `created_at` | DateTime | レコード作成時刻 |
| `updated_at` | DateTime | 最終更新時刻 |
| `scraped_at` | DateTime | データ収集時刻 |

## 🔍 基本的なAPIコール

### 1. 全ての中古車を取得
```http
GET http://localhost:8055/items/used_cars
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Bubble.io設定:**
- API Connector: GETリクエスト
- URL: `http://localhost:8055/items/used_cars`
- ヘッダー: `Authorization: Bearer YOUR_ACCESS_TOKEN`

### 2. IDで単一の車を取得
```http
GET http://localhost:8055/items/used_cars/SOME_UUID_HERE
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 3. 制限された結果を取得（ページネーション）
```http
GET http://localhost:8055/items/used_cars?limit=50&offset=0
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**パラメータ:**
- `limit`: 結果数（最大100）
- `offset`: この数だけ結果をスキップ（ページネーション用）

## 🎯 フィルタリング例

### メーカーでフィルタ（トヨタ車のみ）
```http
GET http://localhost:8055/items/used_cars?filter[maker][_eq]=トヨタ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 価格帯でフィルタ（200-500万円）
```http
GET http://localhost:8055/items/used_cars?filter[price][_gte]=200&filter[price][_lte]=500
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 複数メーカーでフィルタ
```http
GET http://localhost:8055/items/used_cars?filter[maker][_in]=トヨタ,ホンダ,日産
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 地域でフィルタ
```http
GET http://localhost:8055/items/used_cars?filter[region][_contains]=東京
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 走行距離でフィルタ（5万km未満）
```http
GET http://localhost:8055/items/used_cars?filter[mileage][_lt]=50000
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### エンジンタイプでフィルタ（ハイブリッド車のみ）
```http
GET http://localhost:8055/items/used_cars?filter[engine_type][_eq]=ハイブリッド
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### ミッションタイプでフィルタ（オートマのみ）
```http
GET http://localhost:8055/items/used_cars?filter[mission_type][_eq]=AT/CVT
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 駆動方式でフィルタ（4WDのみ）
```http
GET http://localhost:8055/items/used_cars?filter[driving_system][_eq]=4WD
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 修復歴でフィルタ（事故なし）
```http
GET http://localhost:8055/items/used_cars?filter[fix_history][_eq]=なし
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 年式でフィルタ（2020年以降）
```http
GET http://localhost:8055/items/used_cars?filter[model_year][_gte]=2020-01-01
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 価格帯カテゴリでフィルタ
```http
GET http://localhost:8055/items/used_cars?filter[price_range][_eq]=201~300万円
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 装備でフィルタ（ナビ付き車）
```http
GET http://localhost:8055/items/used_cars?filter[comfort_equipment][_contains]=カーナビ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 複合フィルタ（トヨタハイブリッド、300万円未満、東京エリア、事故なし）
```http
GET http://localhost:8055/items/used_cars?filter[maker][_eq]=トヨタ&filter[engine_type][_eq]=ハイブリッド&filter[price][_lt]=300&filter[region][_contains]=東京&filter[fix_history][_eq]=なし
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📋 ソートと並び順

### 価格で並び替え（昇順）
```http
GET http://localhost:8055/items/used_cars?sort=price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 価格で並び替え（降順）
```http
GET http://localhost:8055/items/used_cars?sort=-price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 複数フィールドで並び替え
```http
GET http://localhost:8055/items/used_cars?sort=maker,price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🎨 フィールド選択

### 特定のフィールドのみ取得
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,maker,model,price,mileage,engine_type,region
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 基本車両情報のみ
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,maker,model,model_year,price,mileage,region,image
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 技術仕様
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,displacement,engine_type,mission_type,driving_system,handle
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 装備情報
```http
GET http://localhost:8055/items/used_cars?fields=id,car_name,safe_equipment,comfort_equipment,interior_equipment,exterior_equipment
Authorization: Bearer YOUR_ACCESS_TOKEN
```

これによりレスポンスサイズが削減され、パフォーマンスが向上します。

## 🔍 検索機能

### 車名で検索
```http
GET http://localhost:8055/items/used_cars?filter[car_name][_contains]=プリウス
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### グローバル検索（複数フィールドを横断検索）
```http
GET http://localhost:8055/items/used_cars?search=プリウス
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🛠️ 装備データの操作（JSONフィールド）

装備フィールド（`safe_equipment`、`comfort_equipment`、`interior_equipment`、`exterior_equipment`）には機能のJSON配列が含まれています。

### 装備データ構造例
```json
{
  "safe_equipment": ["パワステ", "ABS", "エアバッグ：運転席/助手席", "横滑り防止装置"],
  "comfort_equipment": ["エアコン", "パワーウィンドウ", "集中ドアロック", "カーナビ"],
  "interior_equipment": ["本革シート", "シートヒーター", "電動シート"],
  "exterior_equipment": ["アルミホイール", "フォグランプ", "ルーフレール"]
}
```

### 特定装備でフィルタ
```http
GET http://localhost:8055/items/used_cars?filter[safe_equipment][_contains]=ABS
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 複数装備機能でフィルタ
```http
GET http://localhost:8055/items/used_cars?filter[comfort_equipment][_contains]=カーナビ&filter[safe_equipment][_contains]=エアバッグ
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 本革シート付き車
```http
GET http://localhost:8055/items/used_cars?filter[interior_equipment][_contains]=本革シート
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### アルミホイール付き車
```http
GET http://localhost:8055/items/used_cars?filter[exterior_equipment][_contains]=アルミホイール
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 📊 集計と統計

### 総車両数をカウント
```http
GET http://localhost:8055/items/used_cars?aggregate[count]=*
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 平均価格
```http
GET http://localhost:8055/items/used_cars?aggregate[avg]=price
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### メーカー別グループ化とカウント
```http
GET http://localhost:8055/items/used_cars?groupBy[]=maker&aggregate[count]=*
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🛠️ Bubble.io連携のコツ

### 1. API Connectorの設定
1. プラグイン → API Connectorに移動
2. 新しいAPI「Directus Used Cars」を追加
3. ベースURL: `http://localhost:8055/items/`
4. 共有ヘッダーを追加: `Authorization: Bearer YOUR_TOKEN`

### 2. コールの作成
各エンドポイントに対して新しいコールを作成：
- **名前**: "Get Used Cars"
- **使用方法**: Data (JSON)
- **データタイプ**: JSON
- **URL**: `used_cars`

### 3. Bubbleでの動的フィルタ
URLパラメータを使用してフィルタを動的にする：
```
used_cars?filter[maker][_eq]=<maker>&filter[price][_lte]=<max_price>
```

### 4. レスポンスの処理
Directusは以下の形式でデータを返します：
```json
{
  "data": [
    {
      "id": "uuid-here",
      "car_name": "プリウス S",
      "maker": "トヨタ",
      "price": 250,
      ...
    }
  ]
}
```

車両リストにアクセスするには：`Result of API call's data`を使用

## 📚 一般的なフィルタ演算子

| 演算子 | 説明 | 例 |
|----------|-------------|---------|
| `_eq` | 等しい | `filter[maker][_eq]=トヨタ` |
| `_neq` | 等しくない | `filter[maker][_neq]=トヨタ` |
| `_lt` | より小さい | `filter[price][_lt]=300` |
| `_lte` | 以下 | `filter[price][_lte]=300` |
| `_gt` | より大きい | `filter[mileage][_gt]=10000` |
| `_gte` | 以上 | `filter[mileage][_gte]=10000` |
| `_in` | リスト内 | `filter[maker][_in]=トヨタ,ホンダ` |
| `_nin` | リスト外 | `filter[maker][_nin]=高級車` |
| `_contains` | テキストを含む | `filter[car_name][_contains]=プリウス` |
| `_ncontains` | テキストを含まない | `filter[car_name][_ncontains]=事故` |
| `_null` | nullである | `filter[detail][_null]=true` |
| `_nnull` | nullでない | `filter[detail][_nnull]=true` |

## 🚨 エラーハンドリング

一般的なHTTPステータスコード：
- `200`: 成功
- `401`: 認証エラー（トークンを確認）
- `403`: 権限なし（権限不足）
- `404`: 見つからない
- `500`: サーバーエラー

## 📖 追加リソース

- [公式Directus API文書](https://docs.directus.io/reference/introduction/)
- [Directusフィルタガイド](https://docs.directus.io/reference/filter-rules/)
- [Directusクエリパラメータ](https://docs.directus.io/reference/query/)
- [認証ガイド](./AUTHENTICATION-GUIDE-JA.md)

## 💡 パフォーマンスのコツ

1. **フィールド選択を使用**してレスポンスサイズを削減
2. **大きなデータセットにはページネーションを実装**
3. **可能な場合はBubbleでレスポンスをキャッシュ**
4. **可能な場合はグローバル検索ではなく特定フィルタを使用**
5. **実際に必要な結果のみに制限**

## 🔧 トラブルシューティング

### CORS問題
CORSエラーが発生した場合、Directusがあなたのドメインを許可するよう設定されていることを確認してください。管理者に連絡して、Bubble.ioのプレビュー/ライブドメインをCORSホワイトリストに追加してもらってください。
