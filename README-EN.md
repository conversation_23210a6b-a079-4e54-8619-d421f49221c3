# Used Car Sales - Nuxt-Directus Management Platform

A Nuxt-Directus based management platform for used car sales data, integrating web scraping capabilities with comprehensive database solutions.

## Architecture

- **Frontend**: Nuxt 3 (with UI components and auto-imports)
- **Database**: CockroachDB (PostgreSQL-compatible) for robust data storage
- **Admin Interface**: Directus for low-code data management
- **Web Scraping**: Playwright-based scraper with flexible output options
- **Environment**: Docker support for containerized development

## Features

- 🚗 **Multi-brand vehicle data management** from CarSensor
- 📊 **Dual output modes**: CSV files and/or database storage
- 🐘 **CockroachDB integration** (Docker setup compatible)
- 🔄 **Batch upsert functionality** (with conflict resolution)
- 📈 **Data deduplication** based on source URLs
- 🚫 **Blacklist filtering** for unwanted vehicles/models
- 🧪 **Health checks** and error handling
- 📱 **Directus admin dashboard**

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 22+ (for local development only)

### 🚀 Development Environment Setup (Recommended)

**Development-optimized hybrid configuration**
- **Nuxt app**: Local execution (fast Hot Module Replacement)
- **Database services**: Docker execution (environment consistency)

**1. Copy environment file:**
```bash
cp .env.example .env
```

**2. Configure environment variables (Important!):**
Update the following values in the `.env` file for production use:
```env
# SECURITY WARNING: Must change these default values for production!
# Generate secure random keys: openssl rand -hex 32
DIRECTUS_KEY=replace-with-secure-32-character-key
DIRECTUS_SECRET=replace-with-secure-64-character-secret
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=admin123
```

**3. Start database services:**
```bash
docker-compose up -d
```

**Services running in Docker:**
- CockroachDB (database)
- Directus (admin interface)
- Redis (cache, for Directus)
- DB initialization service (first run only)

**4. Start Nuxt application locally:**
```bash
npm install
npm run dev
```

**💡 Why this configuration?**
- **Fast development**: Save Nuxt rebuild time (no Docker image rebuilding)
- **Instant feedback**: Code changes reflected instantly in browser
- **Debug efficiency**: Direct access to local logs and DevTools
- **Resource efficiency**: Optimized CPU and memory usage

**Service access:**
- **Main app**: http://localhost:3000
- **Directus admin**: http://localhost:8056 (`<EMAIL>` / `admin123`)
- **Database admin**: http://localhost:8081
- **Redis**: localhost:6379 (internal service, no direct access needed)

## 📚 External Developer API Documentation

For no-code developers using platforms like Bubble.io to integrate with Directus API:

- **[REST API Guide (English)](./docs/REST-API-GUIDE.md)** - Complete guide for accessing used car data via REST API
- **[Authentication Guide (English)](./docs/AUTHENTICATION-GUIDE.md)** - User management and authentication setup guide
- **[REST API Guide (Japanese)](./docs/REST-API-GUIDE-JA.md)** - Complete REST API guide (Japanese)
- **[Authentication Guide (Japanese)](./docs/AUTHENTICATION-GUIDE-JA.md)** - Authentication & user management guide (Japanese)

**For production environment, change `.env`:**
```bash
NODE_ENV=production
```

### Local Development

```bash
# Install dependencies
npm install

# Configure environment variables
cp .env.example .env

# Start development server
npm run dev
```

## Database Management

### Configuration

The application uses CockroachDB for data storage. Configure database connection in `.env`:

```env
DB_HOST=localhost
DB_PORT=26257
DB_NAME=usedcarsales
DB_USER=root
DB_PASSWORD=
DB_SSL=false
```

### Sample Data Generation

Generate test data for development:

```bash
# Generate 10,000 sample records
npm run generate-test-data

# Generate custom quantity (e.g., 1 million records)
npm run generate-sample-data
```

### Database Testing

```bash
# Check database status
npm run check-db

# Run stress tests
npm run stress-test-db
```

## Scraper Integration

### Running the Scraper

```bash
# Run CarSensor scraper with database output
OUTPUT_MODE=database node scraper/carsensor.js

# Run with CSV output
OUTPUT_MODE=csv node scraper/carsensor.js

# Run with both outputs
OUTPUT_MODE=both node scraper/carsensor.js
```

### Default Schedule

By default, the scraper runs every Sunday at 2:00 AM. This schedule can be configured via environment variables.

### API Endpoints

Start scraper via API:

```bash
curl -X POST http://localhost:3000/api/scraper/start \
  -H "Content-Type: application/json" \
  -d '{"outputMode": "database"}'
```

## Project Structure

```
├── scraper/                 # Web scraping functionality
│   ├── lib/                 # Shared scraper utilities
│   ├── database/            # Database connection and schema
│   └── carsensor.js         # Current stable scraper (latest from third-party)
├── pages/                   # Nuxt pages (admin interface)
├── components/              # Vue components
├── composables/             # Nuxt composables
├── scripts/                 # Data generation utility scripts
├── server/                  # Server-side API routes
└── directus/                # Directus configuration
```

## Advanced Features

### Blacklist Management

Filter out unwanted vehicles:

```bash
# Via scripts in scraper directory
node scripts/blacklist.js add maker "Mercedes-Benz" "Too expensive"
node scripts/blacklist.js list
```

### Performance Monitoring

```bash
# Database health check
node scripts/health-check.js
```

## Production Deployment

Build application for production:

```bash
npm run build
```

Preview production build locally:

```bash
npm run preview
```

## Development Notes

- Database automatically initializes with schema and sample records
- Directus provides no-code admin interface for data management
- Scraper scheduling configurable via environment variables
- CockroachDB admin UI available at http://localhost:8081