services:
  # Nuxt Application - Development Build
  # nuxt-app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: usedcar_nuxt_app
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - .:/app
  #     - /app/node_modules
  #     - ./scraper/output:/app/scraper/output
  #     - ./scraper/output_merge:/app/scraper/output_merge
  #   env_file:
  #     - .env
  #   environment:
  #     # Override for Docker networking
  #     - NODE_ENV=${NODE_ENV:-development}
  #     - DB_HOST=cockroach_db
  #     - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      
  #     # Database Configuration
  #     - DB_PORT=${DB_PORT:-26257}
  #     - DB_NAME=${DB_NAME:-usedcarsales}
  #     - DB_USER=${DB_USER:-root}
  #     - DB_PASSWORD=${DB_PASSWORD:-}
  #     - DB_SSL=${DB_SSL:-false}
  #     - DB_POOL_MAX=${DB_POOL_MAX:-20}
  #     - DB_IDLE_TIMEOUT=${DB_IDLE_TIMEOUT:-30000}
  #     - DB_CONNECTION_TIMEOUT=${DB_CONNECTION_TIMEOUT:-10000}
      
  #     # Scraper Configuration
  #     - OUTPUT_MODE=${OUTPUT_MODE:-database}
  #     - BATCH_SIZE=${BATCH_SIZE:-100}
  #     - CSV_OUTPUT_DIR=${CSV_OUTPUT_DIR:-./scraper/output}
  #     - CSV_MERGE_OUTPUT_DIR=${CSV_MERGE_OUTPUT_DIR:-./scraper/output_merge}
      
  #     # Blacklist Configuration
  #     - BLACKLIST_ENABLED=${BLACKLIST_ENABLED:-true}
  #     - BLACKLIST_CACHE_EXPIRY=${BLACKLIST_CACHE_EXPIRY:-300000}
  #     - BLACKLIST_FAIL_OPEN=${BLACKLIST_FAIL_OPEN:-true}
      
  #     # Scraper Scheduling
  #     - SCRAPER_SCHEDULE_ENABLED=${SCRAPER_SCHEDULE_ENABLED:-true}
  #     - SCRAPER_SCHEDULE_CRON=${SCRAPER_SCHEDULE_CRON:-0 0 2 * * 0}
      
  #     # External Access
  #     - EXTERNAL_HOST=${EXTERNAL_HOST:-localhost}
  #     - DIRECTUS_PORT=${DIRECTUS_PORT:-8056}
  #     - DB_UI_PORT=${DB_UI_PORT:-8081}
  #   depends_on:
  #     db-init:
  #       condition: service_completed_successfully
  #   networks:
  #     - usedcar_network
  #   restart: unless-stopped

  cockroach_db:
    image: cockroachdb/cockroach:latest-v24.1
    container_name: usedcar_cockroachdb
    ports:
      - "26257:26257"
      - "${DB_UI_PORT:-8081}:8080"
    command: start-single-node --insecure
    volumes:
      - cockroach_data:/cockroach/cockroach-data
    environment:
      - COCKROACH_DATABASE=usedcarsales
      - COCKROACH_USER=root
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health?ready=1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - usedcar_network

  db-init:
    image: postgres:15-alpine
    container_name: usedcar_db_init
    volumes:
      - ./scraper/database/init:/init-scripts
    environment:
      - PGPASSWORD=""
    command: >
      sh -c "
        echo 'Waiting for CockroachDB to be ready...' &&
        sleep 15 &&
        echo 'Running complete database initialization...' &&
        psql -h cockroach_db -p 26257 -U root -d postgres -f /init-scripts/01-init.sql &&
        echo 'Database initialization completed successfully'
      "
    depends_on:
      cockroach_db:
        condition: service_healthy
    networks:
      - usedcar_network
    restart: "no"

  directus:
    image: directus/directus:latest
    container_name: usedcar_directus
    ports:
      - "${DIRECTUS_PORT:-8056}:8055"
    volumes:
      - ./directus/uploads:/directus/uploads
      - ./directus/extensions:/directus/extensions
      - ./directus/database:/directus/database
    env_file:
      - .env
    environment:
      # Database Configuration (using shared ENV variables)
      DB_CLIENT: cockroachdb
      DB_HOST: cockroach_db
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL: ${DB_SSL}

      # Directus Configuration from ENV variables
      KEY: ${DIRECTUS_KEY}
      SECRET: ${DIRECTUS_SECRET}

      # Admin User from ENV variables
      ADMIN_EMAIL: ${DIRECTUS_ADMIN_EMAIL}
      ADMIN_PASSWORD: ${DIRECTUS_ADMIN_PASSWORD}

      # Public URL from ENV variables
      PUBLIC_URL: ${DIRECTUS_PUBLIC_URL}
 
      # CORS Configuration for client-side access (Bubble.io)
      CORS_ENABLED: true
      CORS_ORIGIN: "http://localhost:3000,http://************:3001"
      CORS_METHODS: "GET,POST,PATCH,DELETE,OPTIONS"
      CORS_ALLOWED_HEADERS: "Content-Type,Authorization"

      # Cache Configuration
      CACHE_ENABLED: false
      CACHE_STORE: redis
      CACHE_TTL: 30m
      CACHE_AUTO_PURGE: true
      CACHE_SCHEMA: true
      
      # Redis Configuration
      REDIS: redis://redis:6379

      # Force fresh installation
      RESET_ADMIN: true
    depends_on:
      db-init:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    networks:
      - usedcar_network
    restart: unless-stopped

  # Redis Cache (optional, for Directus caching)
  redis:
    image: redis:7-alpine
    container_name: usedcar_redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - usedcar_network
    restart: unless-stopped

volumes:
  cockroach_data:
    driver: local
  redis_data:
    driver: local

networks:
  usedcar_network:
    driver: bridge
