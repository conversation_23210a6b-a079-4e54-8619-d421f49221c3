// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-08-09',
  devtools: { enabled: true },
  modules: ['@nuxt/ui-pro', '@formkit/auto-animate/nuxt', 'nuxt-easy-lightbox', '@vueuse/nuxt'],
  css: ['~/assets/css/main.css'],
  ssr: false,
  nitro: {
    experimental: {
      tasks: true
    },
    scheduledTasks: {
      ['0 0 1 * *']: ['scraper:run'], // Run every 1st day of the month
      ['0 0 14 * *']: ['scraper:run'] // run every 14th day of the month
    }
  },

  routeRules:{
    '/debug': { ssr: true }
  },

  ui: {
    colorMode: false
  },

  runtimeConfig: {
    // Server-side only environment variables
    db:{
      host: 'localhost',
      port: '26257',
      name: 'usedcarsales',
      user: 'root',
      password: process.env.DB_PASSWORD || '',
      ssl: process.env.DB_SSL === 'true',
      poolMax: parseInt(process.env.DB_POOL_MAX || '20'),
      idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    },

    // Scraper configuration
    outputMode: process.env.OUTPUT_MODE || 'both',
    batchSize: parseInt(process.env.BATCH_SIZE || '100'),
    csvOutputDir: process.env.CSV_OUTPUT_DIR || './scraper/output',
    csvMergeOutputDir: process.env.CSV_MERGE_OUTPUT_DIR || './scraper/output_merge',

    // Blacklist configuration
    blacklist:{
      enabled: process.env.BLACKLIST_ENABLED === 'true',
      cacheExpiry: parseInt(process.env.BLACKLIST_CACHE_EXPIRY || '300000'),
      failOpen: process.env.BLACKLIST_FAIL_OPEN === 'true',
    },

    // Public variables (exposed to client-side)
    public: {
      directus: {
        url: process.env.DIRECTUS_PUBLIC_URL || 'http://localhost:8056', 
        port: '8056',
      },
      externalHost: 'localhost',
      dbUiPort: '8081',
      scraperScheduleCron: process.env.SCRAPER_SCHEDULE_CRON || '0 2 * * 0',
    }
  },

  experimental: {
    typedPages: true
  }
})