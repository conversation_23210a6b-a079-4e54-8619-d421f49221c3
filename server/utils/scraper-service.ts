import { spawn } from 'child_process'
import { join } from 'path'
import { getLastScraperRun, isScraperRunning, createScraperRun, updateScraperRun } from './database'

export interface ScraperStatus {
  isRunning: boolean
  lastRun: Date | null
  nextRun: Date | null
  error: string | null
}

let nextRunTime: Date | null = null

/**
 * Get current scraper status from database
 */
export async function getScraperStatus(): Promise<ScraperStatus> {
  try {
    const [isRunning, lastRunData] = await Promise.all([
      isScraperRunning(),
      getLastScraperRun()
    ])
    
    return {
      isRunning,
      lastRun: lastRunData?.lastRun ? new Date(lastRunData.lastRun) : null,
      nextRun: nextRunTime,
      error: lastRunData?.error || null
    }
  } catch (error) {
    console.error('Failed to get scraper status from database:', error)
    // Fallback to basic status
    return {
      isRunning: false,
      lastRun: null,
      nextRun: nextRunTime,
      error: 'Failed to connect to database'
    }
  }
}

/**
 * Run the scraper with specified output mode
 */
export async function runScraper(outputMode: 'csv' | 'database' | 'both' = 'both', triggerSource: 'manual' | 'cron' | 'api' = 'manual'): Promise<{ success: boolean; message: string }> {
  // Check if scraper is already running
  const isRunning = await isScraperRunning()
  if (isRunning) {
    return {
      success: false,
      message: 'Scraper is already running'
    }
  }

  const config = useRuntimeConfig()
  const scraperPath = join(process.cwd(), 'scraper', 'carsensor.js')

  // Create database record for this run
  let runRecord
  try {
    runRecord = await createScraperRun(outputMode, triggerSource)
  } catch (error) {
    console.error('Failed to create scraper run record:', error)
    return {
      success: false,
      message: 'Failed to initialize scraper run tracking'
    }
  }

  return new Promise((resolve) => {
    console.log(`🚀 Starting scraper with output mode: ${outputMode} (Run ID: ${runRecord.id})`)

    // Set environment variables for the scraper process
    const env = {
      ...process.env,
      NUXT_ENV_INIT: 'true', // Signal that we're running from Nuxt
      OUTPUT_MODE: outputMode,
      SCRAPER_RUN_ID: runRecord.id, // Pass the run ID to the scraper
      DB_HOST: config.db.host,
      DB_PORT: config.db.port,
      DB_NAME: config.db.name,
      DB_USER: config.db.user,
      DB_PASSWORD: config.db.password,
      DB_SSL: config.db.ssl.toString(),
      DB_POOL_MAX: config.db.poolMax.toString(),
      DB_IDLE_TIMEOUT: config.db.idleTimeout.toString(),
      DB_CONNECTION_TIMEOUT: config.db.connectionTimeout.toString(),
      BATCH_SIZE: config.batchSize.toString(),
      CSV_OUTPUT_DIR: config.csvOutputDir,
      CSV_MERGE_OUTPUT_DIR: config.csvMergeOutputDir,
      // Enhanced scraper configuration
      SCRAPER_BRAND_MAX_ATTEMPTS: process.env.SCRAPER_BRAND_MAX_ATTEMPTS || '3',
      SCRAPER_PAGE_MAX_ATTEMPTS: process.env.SCRAPER_PAGE_MAX_ATTEMPTS || '3',
      SCRAPER_DETAIL_MAX_ATTEMPTS: process.env.SCRAPER_DETAIL_MAX_ATTEMPTS || '3',
      SCRAPER_BRAND_BASE_DELAY_MS: process.env.SCRAPER_BRAND_BASE_DELAY_MS || '5000',
      SCRAPER_PAGE_BASE_DELAY_MS: process.env.SCRAPER_PAGE_BASE_DELAY_MS || '2000',
      SCRAPER_DETAIL_BASE_DELAY_MS: process.env.SCRAPER_DETAIL_BASE_DELAY_MS || '1000',
      SCRAPER_BACKOFF_MULTIPLIER: process.env.SCRAPER_BACKOFF_MULTIPLIER || '3',
      EMAIL_NOTIFICATIONS_ENABLED: process.env.EMAIL_NOTIFICATIONS_ENABLED || 'false',
      SMTP_HOST: process.env.SMTP_HOST || 'smtp.resend.com',
      SMTP_PORT: process.env.SMTP_PORT || '587',
      SMTP_SECURE: process.env.SMTP_SECURE || 'false',
      SMTP_USER: process.env.SMTP_USER || '',
      SMTP_PASS: process.env.SMTP_PASS || '',
      FROM_EMAIL: process.env.FROM_EMAIL || '',
      TO_EMAILS: process.env.TO_EMAILS || '',
      EMAIL_ALERT_COOLDOWN_MINUTES: process.env.EMAIL_ALERT_COOLDOWN_MINUTES || '30',
      MAX_BRANDS_TO_PROCESS: process.env.MAX_BRANDS_TO_PROCESS || '0',
      SKIP_FAILED_BRANDS: process.env.SKIP_FAILED_BRANDS || 'true',
      SAVE_PROGRESS_FILE: process.env.SAVE_PROGRESS_FILE || 'true'
    }

    const scraperProcess = spawn('node', [scraperPath], {
      cwd: join(process.cwd(), 'scraper'),
      env,
      stdio: 'pipe'
    })

    let output = ''
    let errorOutput = ''

    scraperProcess.stdout?.on('data', (data) => {
      const chunk = data.toString()
      output += chunk
      console.log(`[Scraper] ${chunk.trim()}`)
    })

    scraperProcess.stderr?.on('data', (data) => {
      const chunk = data.toString()
      errorOutput += chunk
      console.error(`[Scraper Error] ${chunk.trim()}`)
    })

    scraperProcess.on('close', async (code) => {
      try {
        if (code === 0) {
          console.log(`✅ Scraper completed successfully (Run ID: ${runRecord.id})`)
          await updateScraperRun(runRecord.id, 'completed', {
            errorMessage: null
          })
          resolve({
            success: true,
            message: 'Scraper completed successfully'
          })
        } else {
          const errorMessage = `Scraper failed with exit code ${code}`
          console.error(`❌ ${errorMessage} (Run ID: ${runRecord.id})`)
          await updateScraperRun(runRecord.id, 'failed', {
            errorMessage: errorOutput || errorMessage
          })
          resolve({
            success: false,
            message: errorMessage
          })
        }
      } catch (updateError) {
        console.error('Failed to update scraper run record:', updateError)
        resolve({
          success: code === 0,
          message: code === 0 ? 'Scraper completed successfully' : `Scraper failed with exit code ${code}`
        })
      }
    })

    scraperProcess.on('error', async (error) => {
      console.error('❌ Failed to start scraper:', error.message)
      try {
        await updateScraperRun(runRecord.id, 'failed', {
          errorMessage: `Failed to start scraper: ${error.message}`
        })
      } catch (updateError) {
        console.error('Failed to update scraper run record:', updateError)
      }
      resolve({
        success: false,
        message: `Failed to start scraper: ${error.message}`
      })
    })
  })
}

/**
 * Schedule next scraper run
 */
export function scheduleNextRun(delayMs: number) {
  nextRunTime = new Date(Date.now() + delayMs)
}

/**
 * Clear scheduled run
 */
export function clearScheduledRun() {
  nextRunTime = null
}
