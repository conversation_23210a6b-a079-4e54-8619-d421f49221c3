import { Pool } from 'pg'

let pool: Pool | null = null

/**
 * Get database connection pool using Nuxt runtime config
 */
export function getDatabasePool() {
  if (pool) {
    return pool
  }

  const config = useRuntimeConfig()

  pool = new Pool({
    host: config.db.host,
    port: parseInt(config.db.port),
    database: config.db.name,
    user: config.db.user,
    password: config.db.password,
    ssl: config.db.ssl ? { rejectUnauthorized: false } : false,
    max: config.db.poolMax,
    idleTimeoutMillis: config.db.idleTimeout,
    connectionTimeoutMillis: config.db.connectionTimeout,
  })

  return pool
}

/**
 * Execute a database query
 */
export async function executeQuery(text: string, params: any[] = []) {
  const pool = getDatabasePool()
  const client = await pool.connect()
  
  try {
    const result = await client.query(text, params)
    return result
  } finally {
    client.release()
  }
}

/**
 * Check database health
 */
export async function checkDatabaseHealth() {
  try {
    const result = await executeQuery('SELECT NOW() as timestamp, version() as version')
    return {
      status: 'healthy',
      timestamp: result.rows[0].timestamp,
      version: result.rows[0].version,
      connected: true
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      connected: false
    }
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats() {
  try {
    const countResult = await executeQuery('SELECT COUNT(*) as total_cars FROM used_cars')
    const recentResult = await executeQuery(`
      SELECT COUNT(*) as recent_cars 
      FROM used_cars 
      WHERE created_at > NOW() - INTERVAL '7 days'
    `)
    const makersResult = await executeQuery(`
      SELECT maker, COUNT(*) as count 
      FROM used_cars 
      WHERE maker IS NOT NULL 
      GROUP BY maker 
      ORDER BY count DESC 
      LIMIT 10
    `)

    return {
      totalCars: parseInt(countResult.rows[0].total_cars),
      recentCars: parseInt(recentResult.rows[0].recent_cars),
      topMakers: makersResult.rows
    }
  } catch (error) {
    throw new Error(`Failed to get database stats: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get the last scraper run information
 */
export async function getLastScraperRun() {
  try {
    const result = await executeQuery(`
      SELECT started_at, completed_at, status, trigger_source, output_mode, error_message
      FROM scraper_runs 
      ORDER BY started_at DESC 
      LIMIT 1
    `)
    
    if (result.rows.length === 0) {
      return null
    }
    
    const run = result.rows[0]
    return {
      lastRun: run.completed_at || run.started_at,
      status: run.status,
      triggerSource: run.trigger_source,
      outputMode: run.output_mode,
      error: run.error_message
    }
  } catch (error) {
    console.error('Failed to get last scraper run:', error)
    return null
  }
}

/**
 * Create a new scraper run record
 */
export async function createScraperRun(outputMode: 'csv' | 'database' | 'both', triggerSource: 'manual' | 'cron' | 'api' = 'manual') {
  try {
    const result = await executeQuery(`
      INSERT INTO scraper_runs (output_mode, trigger_source, status)
      VALUES ($1, $2, 'running')
      RETURNING id, started_at
    `, [outputMode, triggerSource])
    
    return {
      id: result.rows[0].id,
      startedAt: result.rows[0].started_at
    }
  } catch (error) {
    console.error('Failed to create scraper run record:', error)
    throw error
  }
}

/**
 * Update scraper run with completion details
 */
export async function updateScraperRun(
  runId: string, 
  status: 'completed' | 'failed' | 'cancelled',
  details: {
    carsProcessed?: number
    carsInserted?: number
    carsUpdated?: number
    errorsCount?: number
    errorMessage?: string | null
  } = {}
) {
  try {
    const completedAt = new Date()
    const result = await executeQuery(`
      UPDATE scraper_runs 
      SET 
        completed_at = $2,
        status = $3,
        cars_processed = COALESCE($4, cars_processed),
        cars_inserted = COALESCE($5, cars_inserted),
        cars_updated = COALESCE($6, cars_updated),
        errors_count = COALESCE($7, errors_count),
        error_message = $8,
        duration_seconds = EXTRACT(EPOCH FROM ($2 - started_at))::INTEGER,
        updated_at = NOW()
      WHERE id = $1
      RETURNING started_at, completed_at
    `, [
      runId, 
      completedAt, 
      status,
      details.carsProcessed,
      details.carsInserted,
      details.carsUpdated,
      details.errorsCount,
      details.errorMessage
    ])
    
    return result.rows[0]
  } catch (error) {
    console.error('Failed to update scraper run record:', error)
    throw error
  }
}

/**
 * Check if scraper is currently running in database
 * Considers a scraper as not running if:
 * - No running status found within 2 hours, OR
 * - More than 2 days have passed since the last run (handles server restarts)
 */
export async function isScraperRunning() {
  try {
    const result = await executeQuery(`
      SELECT
        COUNT(*) FILTER (WHERE status = 'running' AND started_at > NOW() - INTERVAL '12 hours') as recent_running_count,
        MAX(started_at) as last_run_time
      FROM scraper_runs
    `)

    const recentRunningCount = parseInt(result.rows[0].recent_running_count) || 0
    const lastRunTime = result.rows[0].last_run_time

    // If there are recent running entries, consider it running
    if (recentRunningCount > 0) {
      return true
    }

    // If no recent running entries, check if it's been more than 2 days since last run
    // If so, consider any "running" status as stale (server restart scenario)
    if (lastRunTime) {
      const lastRun = new Date(lastRunTime)
      const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)

      if (lastRun < twoDaysAgo) {
        return false // Consider stale if last run was more than 2 days ago
      }
    }

    return false
  } catch (error) {
    console.error('Failed to check if scraper is running:', error)
    return false
  }
}

/**
 * Close database connection pool
 */
export async function closeDatabasePool() {
  if (pool) {
    await pool.end()
    pool = null
  }
}
