export default defineEventHandler(async (event) => {
  try {
    const health = await checkDatabaseHealth()
    
    return {
      success: health.status === 'healthy',
      health,
      checkedAt: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Database health check failed:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Database health check failed'
    })
  }
})
