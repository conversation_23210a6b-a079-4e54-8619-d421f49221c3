import { getDatabaseStats } from '../../utils/database'

export default defineEventHandler(async (event) => {
  try {
    const stats = await getDatabaseStats()
    
    return {
      success: true,
      stats,
      retrievedAt: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Failed to get database stats:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Failed to get database stats'
    })
  }
})
