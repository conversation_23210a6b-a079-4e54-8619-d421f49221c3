import { getScraperStatus } from '../../utils/scraper-service'

export default defineEventHandler(async (event) => {
  try {
    const status = await getScraperStatus()
    
    return {
      success: true,
      status: {
        isRunning: status.isRunning,
        lastRun: status.lastRun?.toISOString() || null,
        nextRun: status.nextRun?.toISOString() || null,
        error: status.error
      },
      checkedAt: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Failed to get scraper status:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Failed to get scraper status'
    })
  }
})
