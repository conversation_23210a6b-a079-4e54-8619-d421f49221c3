export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const outputMode = body?.outputMode || 'database'
    
    // Validate output mode
    if (!['csv', 'database', 'both'].includes(outputMode)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid output mode. Must be csv, database, or both'
      })
    }

    console.log(`🚀 API request to start scraper with mode: ${outputMode}`)
    
    // Run the scraper task
    const result = await runTask('scraper:run', {
      payload: { outputMode }
    })
    
    return {
      success: true,
      message: 'Scraper started successfully',
      outputMode,
      taskResult: result,
      startedAt: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Failed to start scraper:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Failed to start scraper'
    })
  }
})
