export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    
    // Directus internal container connection (hardcoded - won't change)
    const directusUrl = config.public.directus.url
    
    // Try to fetch Directus server info endpoint
    const response = await fetch(`${directusUrl}/server/info`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      // 5 second timeout
      signal: AbortSignal.timeout(5000)
    })
    
    if (response.ok) {
      const data = await response.json()
      
      return {
        success: true,
        status: {
          accessible: true,
          version: data.directus?.version || 'unknown',
          uptime: data.directus?.flows?.uptime || null,
          cache: {
            enabled: !!process.env.DIRECTUS_CACHE_ENABLED,
            store: process.env.DIRECTUS_CACHE_STORE || 'memory',
            ttl: process.env.DIRECTUS_CACHE_TTL || '30m'
          }
        }
      }
    } else {
      return {
        success: false,
        status: {
          accessible: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        }
      }
    }
    
  } catch (error) {
    return {
      success: false,
      status: {
        accessible: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      }
    }
  }
})