import { checkDatabaseHealth } from '../utils/database'

export default defineTask({
  meta: {
    name: 'db:health',
    description: 'Check database health and connectivity'
  },
  async run() {
    console.log('🏥 Running database health check...')

    try {
      const health = await checkDatabaseHealth()

      if (health.status === 'healthy') {
        console.log('✅ Database is healthy')
        console.log(`   Server time: ${health.timestamp}`)
        console.log(`   Version: ${health.version}`)

        return {
          result: {
            success: true,
            status: health.status,
            timestamp: health.timestamp,
            version: health.version,
            checkedAt: new Date().toISOString()
          }
        }
      } else {
        console.error('❌ Database is unhealthy:', health.error)
        return {
          result: {
            success: false,
            status: health.status,
            error: health.error,
            checkedAt: new Date().toISOString()
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('❌ Database health check failed:', errorMessage)
      return {
        result: {
          success: false,
          status: 'error',
          error: errorMessage,
          checkedAt: new Date().toISOString()
        }
      }
    }
  }
})
