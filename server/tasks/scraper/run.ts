import { runScraper } from '../../utils/scraper-service'

export default defineTask({
  meta: {
    name: 'scraper:run',
    description: 'Run the car scraper manually'
  },
  async run({ payload }) {
    console.log('🚀 Nitro Task: Starting scraper run...')

    const outputMode = (payload?.outputMode as 'csv' | 'database' | 'both') || 'database'

    try {
      const result = await runScraper(outputMode, 'api')

      if (result.success) {
        console.log('✅ Scraper task completed successfully')
        return {
          result: {
            success: true,
            message: result.message,
            completedAt: new Date().toISOString()
          }
        }
      } else {
        console.error('❌ Scraper task failed:', result.message)
        return {
          result: {
            success: false,
            message: result.message,
            completedAt: new Date().toISOString()
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('❌ Scraper task error:', errorMessage)
      return {
        result: {
          success: false,
          message: errorMessage,
          completedAt: new Date().toISOString()
        }
      }
    }
  }
})
