export default function useUser() {
  const { dFetch } = useDirectus();
  const profile = useState<DirectusUsers | null>("profile", () => null);
  const leftOffUrl = useState<string | null>("leftOffUrl", () => null);
  const isAuthenticated = useCookie("isAuthenticated", {
    default: () => false,
  });

  const login = async (email: string, password: string) => {
    try {
      await dFetch("/auth/login", {
        method: "POST",
        body: {
          email,
          password,
          mode: "session",
        },
      });

      isAuthenticated.value = true;

      // Get user profile
      const user = await dFetch<DirectusUsers>("/users/me");
      profile.value = user;
    } catch (err) {
      console.error("Failed to login:", err);
      throw err;
    }
  };

  const logout = async () => {
    try {
      await dFetch("/auth/logout", {
        method: "POST",
        body: {
          mode: "session",
        },
      });
      isAuthenticated.value = false;
      profile.value = null;
      leftOffUrl.value = null;
      navigateTo("/login");
    } catch (err) {
      console.error("Failed to logout:", err);
      throw err;
    }
  };

  const refresh = async () => {
    try {
      await dFetch("/auth/refresh", {
        method: "POST",
        body: {
          mode: "session",
        },
      });
    } catch (err) {
      console.error("Failed to refresh:", err);
      throw err;
    }
  };

  return {
    profile,
    isAuthenticated,
    leftOffUrl,
    login,
    logout,
    refresh,
  };
}
