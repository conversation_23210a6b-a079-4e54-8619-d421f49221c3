type Maker = (typeof MAKERS)[0];

export const useScrollPosition = () => {
  const homeMakerSelected = ref<Maker | null>(null)
  const homeMakerPosition = ref(0)
  const homeModelPosition = ref(0)
  
  // Load from sessionStorage on initialization
  if (process.client) {
    const savedMaker = sessionStorage.getItem('homeMakerSelected')
    const savedMakerPosition = sessionStorage.getItem('homeMakerPosition')
    const savedModelPosition = sessionStorage.getItem('homeModelPosition')
    
    if (savedMaker) {
      try {
        homeMakerSelected.value = JSON.parse(savedMaker)
      } catch (e) {
        console.error('Failed to parse saved maker:', e)
      }
    }
    
    if (savedMakerPosition) {
      homeMakerPosition.value = parseInt(savedMakerPosition, 10) || 0
    }
    
    if (savedModelPosition) {
      homeModelPosition.value = parseInt(savedModelPosition, 10) || 0
    }
  }
  
  // Save to sessionStorage when values change
  watch(homeMakerSelected, (newValue) => {
    if (process.client) {
      if (newValue) {
        sessionStorage.setItem('homeMakerSelected', JSON.stringify(newValue))
      } else {
        sessionStorage.removeItem('homeMakerSelected')
      }
    }
  }, { deep: true })
  
  watch(homeMakerPosition, (newValue) => {
    if (process.client) {
      sessionStorage.setItem('homeMakerPosition', newValue.toString())
    }
  })
  
  watch(homeModelPosition, (newValue) => {
    if (process.client) {
      sessionStorage.setItem('homeModelPosition', newValue.toString())
    }
  })
  
  return {
    homeMakerSelected,
    homeMakerPosition,
    homeModelPosition
  }
}