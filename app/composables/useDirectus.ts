import { defu } from "defu";
import type { UseFetchOptions } from "#app";

export default function () {
	const config = useRuntimeConfig();
	const userToken = useCookie("user_token");

	// Equivalent to useFetch with REST API
	const useDirectusFetch = <T>(
		url: string | (() => string),
		options: UseFetchOptions<T> = {},
	) => {
		const defaults: UseFetchOptions<T> = {
			baseURL: config.public.directus.url,
			credentials: "include",
			onResponse(_ctx) {
				// For REST API, data is directly in response
				if (_ctx.response._data?.data) {
					_ctx.response._data = _ctx.response._data.data;
				}
			},
			lazy: true,
		};

		const params = defu(options, defaults);

		return useFetch(url, params);
	};

	// Equivalent to $fetch with REST API
	const dFetch = async <T>(url: string, options: any = {}): Promise<T> => {
		const defaults = {
			baseURL: config.public.directus.url,
			headers: {
				Accept: "application/json",
				"Content-Type": "application/json",
			},
			credentials: "include" as RequestCredentials,
		};

		const params = defu(options, defaults);
		const response = await $fetch<any>(url, params);
		
		// For REST API, extract data if it exists
		return response?.data || response;
	};

	return {
		useDirectusFetch,
		dFetch,
	};
}
