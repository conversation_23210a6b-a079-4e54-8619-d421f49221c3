type Maker = (typeof MAKERS)[0];

export default function () {
  const { dFetch } = useDirectus();
  const blacklistedMakers = useState<Maker[]>("blacklistedMakers", () => []);
  const blacklistedModels = useState<string[]>("blacklistedModels", () => []);

  const getBlacklists = async () => {
    try {
      const blacklist = await dFetch<Blacklist[]>("/items/blacklist");

      // use the slugs to filter the makers from MAKERS const
      blacklistedMakers.value = MAKERS.filter((maker) =>
        blacklist
          .filter((item) => item.type === "maker")
          .map((item) => item.value)
          .includes(maker.slug)
      );
      blacklistedModels.value = blacklist
        .filter((item) => item.type === "model")
        .map((item) => item.value);
    } catch (error) {
      console.error("Failed to fetch blacklisted makers:", error);
    }
  };

  return {
    blacklistedMakers,
    blacklistedModels,
    getBlacklists,
  };
}
