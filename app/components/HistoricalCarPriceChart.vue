<script lang="ts" setup>
// @ts-ignore
import VueApexCharts from 'vue3-apexcharts';

const props = defineProps<{ car: UsedCars }>();
const { useDirectusFetch } = useDirectus();

const showOnlyAvailableData = ref(false);
const selectedPeriod = ref("6m");

const periodItems = [
  // { label: "3ヶ月", value: "3m", months: 3 },
  { label: "6ヶ月", value: "6m", months: 6 },
  { label: "1年", value: "1y", months: 12 },
  { label: "2年", value: "2y", months: 24 },
  // { label: "5年", value: "5y", months: 60 },
  { label: "10年", value: "10y", months: 120 },
];

const { data: historicalPrices, status: historicalPricesStatus } = await
useDirectusFetch<{ price: string; scraped_at: string }[]>(`/items/used_cars`, {
  server: false,
  key: `historical-prices-${props.car.id}`,
  params: {
    sort: "scraped_at",
    filter: {
      source_url: {
        _eq: computed(() => props.car.source_url),
      },
    },
    fields: ["price", "scraped_at"],
  },
});

// const exampleChartData = [
//   { scraped_at: new Date(), price: 186 },
//   { scraped_at: new Date(), price: 305 },
//   { scraped_at: new Date(), price: 237 },
//   { scraped_at: new Date(), price: 260 },
//   { scraped_at: new Date(), price: 209 },
//   { scraped_at: new Date(), price: 250 },
// ];

const chartData = computed(() => {
  const selectedOption = periodItems.find(option => option.value === selectedPeriod.value);
  const MONTHS_TO_SHOW = selectedOption ? selectedOption.months : 12;
  
  // Create array of past X months
  const monthsArray = [];
  const now = new Date();
  
  for (let i = MONTHS_TO_SHOW - 1; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = date.toLocaleString("ja", {
      timeZone: "Asia/Tokyo",
      year: "numeric",
      month: "2-digit",
    });
    monthsArray.push({
      scraped_at: monthKey,
      price: null as number | null,
    });
  }

  // Group actual data by year-month
  const monthlyGroups = new Map<string, number[]>();
  
  if (historicalPrices.value && historicalPrices.value.length > 0) {
    historicalPrices.value.forEach((item) => {
      const date = new Date(item.scraped_at);
      const monthKey = date.toLocaleString("ja", {
        timeZone: "Asia/Tokyo",
        year: "numeric",
        month: "2-digit",
      });
      
      if (!monthlyGroups.has(monthKey)) {
        monthlyGroups.set(monthKey, []);
      }
      
      const price = Number(item.price);
      if (price > 0) { // Only include valid prices
        monthlyGroups.get(monthKey)?.push(price);
      }
    });
  }

  // Fill in actual data where available
  const result = monthsArray.map((month) => {
    const prices = monthlyGroups.get(month.scraped_at);
    if (prices && prices.length > 0) {
      const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
      return {
        ...month,
        price: Math.round(averagePrice * 100) / 100, // Round to 2 decimal places
      };
    }
    return {  ...month, price: undefined }; // Keep null price for months without data
  });

  // If showOnlyAvailableData is true, filter out null prices
  if (showOnlyAvailableData.value) {
    return result.filter(item => item.price !== undefined);
  }
  
  return result;
});

// 使用されていない型定義を削除

const xFormatter = (i: number): string => `${chartData.value[i]?.scraped_at || ""}`;

// ApexCharts configuration
const chartOptions = computed(() => ({
  chart: {
    type: 'area',
    toolbar: {
      show: false
    },
    zoom: {
      enabled: false
    }
  },
  colors: ['#0084d1'],
  dataLabels: {
    enabled: false
  },
  stroke: {
    curve: 'smooth',
    width: 2
  },
  fill: {
    type: 'gradient',
    gradient: {
      shadeIntensity: 1,
      opacityFrom: 0.7,
      opacityTo: 0.2,
      stops: [0, 90, 100]
    }
  },
  xaxis: {
    categories: chartData.value.map(item => item.scraped_at),
    labels: {
      style: {
        fontSize: '12px'
      }
    },
    title: {
      text: '月',
      style: {
        fontSize: '14px'
      }
    }
  },
  yaxis: {
    labels: {
      style: {
        fontSize: '12px'
      },
      formatter: function (val: number) {
        return val + ' 万円';
      }
    },
    title: {
      text: '価格 (万円)',
      style: {
        fontSize: '14px'
      }
    }
  },
  grid: {
    xaxis: {
      lines: {
        show: true
      }
    },
    yaxis: {
      lines: {
        show: true
      }
    }
  },
  tooltip: {
    y: {
      formatter: function (val: number) {
        return val + ' 万円';
      }
    }
  },
  legend: {
    show: false
  }
}));

const chartSeries = computed(() => [{
  name: '価格 (万円)',
  data: chartData.value.map(item => item.price || 0)
}]);
</script>
<template>
  <div v-if="historicalPricesStatus === 'success'">
    <!-- Chart controls -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center gap-4">
        <h3 class="text-lg font-semibold">価格時系列グラフ</h3>
        <USelect
          v-model="selectedPeriod"
          :items="periodItems"
          option-attribute="label"
          value-attribute="value"
          class="min-w-24"
        />
      </div>
      <!-- <USwitch 
        v-model="showOnlyAvailableData" 
        :ui="{
          base: 'relative inline-flex items-center cursor-pointer',
        }"
      >
        <template #label>
          <span class="text-sm text-gray-600 ml-2">
            価格のある月のみ表示
          </span>
        </template>
      </USwitch> -->
    </div>
    
    <!-- Chart -->
    <VueApexCharts
      :options="chartOptions"
      :series="chartSeries"
      :height="500"
      type="area"
    />
  </div>
</template>
