<template>
  <!-- Skeleton card -->
  <div
    v-for="i in 13"
    :key="i"
    class="p-6 gap-3 outline-1 outline-gray-100 shadow rounded-lg flex justify-start animate-pulse"
  >
    <!-- Image skeleton -->
    <div class="flex flex-col h-full">
      <div class="w-full max-w-xs bg-gray-200 rounded aspect-video"></div>
      <div class="flex gap-1 justify-start pt-2 max-w-xs">
        <div
          v-for="j in 4"
          :key="j"
          class="max-h-8 aspect-square bg-gray-200 rounded"
        ></div>
      </div>
    </div>

    <!-- Car info skeleton -->
    <div class="flex flex-col gap-2 w-full">
      <!-- Car name skeleton -->
      <div class="h-7 bg-gray-200 rounded w-3/4"></div>

      <!-- Details section skeleton -->
      <div class="grid gap-4 grid-cols-12">
        <div class="col-span-4">
          <div class="h-8 bg-gray-200 rounded w-20 mb-2"></div>
          <div class="h-16 bg-gray-200 rounded w-32"></div>
        </div>
        <div class="grid grid-cols-2 col-span-8 gap-4">
          <div v-for="k in 4" :key="k">
            <div class="h-3 bg-gray-200 rounded w-12 mb-1"></div>
            <div class="h-4 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>