export default defineNuxtRouteMiddleware(async (to, from) => {
  const { leftOffUrl, isAuthenticated, refresh } = useUser();
  if (to.path === "/login") {
    return;
  }

  // Check session still valid
  try {
    await refresh();
  } catch {
    isAuthenticated.value = false;
    return navigateTo("/login");
  }

  if (!isAuthenticated.value) {
    leftOffUrl.value = to.fullPath;
    return navigateTo("/login");
  }
});
