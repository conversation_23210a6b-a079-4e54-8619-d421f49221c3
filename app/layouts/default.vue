<script setup lang="ts">
import type { NavigationMenuItem } from "@nuxt/ui";
const open = ref(false);
const { logout, isAuthenticated } = useUser();

const links = [
  {
    label: "検索",
    icon: "i-lucide-search",
    to: "/",
    external: true,
  },
  {
    label: "車両確認",
    icon: "i-lucide-car",
    to: "/cars",
  },
  {
    label: "設定",
    icon: "i-lucide-cog",
    to: "/settings",
  },
] satisfies NavigationMenuItem[];

const groups = computed(() => [
  {
    id: "links",
    label: "ページ", //pages
    items: links.flat(),
  },
]);
</script>

<template>
  <UDashboardGroup unit="rem">
    <UDashboardSidebar
      id="default"
      v-model:open="open"
      collapsible
      resizable
      class="bg-elevated/25"
      :ui="{ footer: 'lg:border-t lg:border-default' }"
    >
      <template #default="{ collapsed }">
        <div
          id="branding"
          class="flex items-center h-12 font-bold px-2 w-full transition-all duration-200"
          :class="collapsed ? 'justify-center' : 'justify-between'"
        >
          <p v-if="!collapsed" class="truncate">中古車横断システム</p>
          <UDashboardSidebarCollapse />
        </div>

        <UNavigationMenu
          :collapsed="collapsed"
          :items="links"
          orientation="vertical"
          tooltip
          popover
          :ui="{
            item: 'my-4',
            link: 'py-4 hover:bg-primary-100 focus:bg-primary-100 rounded-lg',
          }"
        />
      </template>

      <template #footer="{ collapsed }">
        <!-- logout -->
        <UButton
          v-if="isAuthenticated"
          id="logout"
          variant="ghost"
          color="neutral"
          size="sm"
          icon="i-lucide-log-out"
          @click="logout"
        >
          {{ collapsed ? '' : 'ログアウト' }}
        </UButton>
        <UButton
          v-else
          variant="ghost"
          color="neutral"
          size="sm"
          to="/login"
        >
          ログイン
        </UButton>
        <p v-on:dblclick="navigateTo('/debug')" v-if="!collapsed" class="text-dimmed text-xs ml-auto">v1.1.0</p>
      </template>
    </UDashboardSidebar>

    <UDashboardSearch :groups="groups" />

    <slot />
  </UDashboardGroup>
</template>
