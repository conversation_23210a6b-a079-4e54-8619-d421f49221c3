<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title="設定">
        <template #leading>
        </template>
      </UDashboardNavbar>

      <!-- <UDashboardToolbar>
        <template #left>
          asdsadasdas
        </template>
      </UDashboardToolbar> -->
    </template>

    <template #body>
      <h2 class="text-3xl font-bold">設定</h2>

      <!-- Manage Blacklisted Car Keywords -->
      <div>
        <h4 class="text-xl font-bold">ブラックリストのキーワード</h4>
        <p class="text-sm text-muted">
          検索結果から除外するキーワードを管理します。
        </p>

        <!-- How it works explanation -->
        <UAlert
          icon="i-heroicons-information-circle"
          color="blue"
          variant="soft"
          title="ブラックリストの仕組み"
          description="車両タイトルにキーワードが含まれる車両をスクレイピング時に自動除外します。"
          class="mt-4"
        />

        <!-- Usage examples -->
        <UAlert
          icon="i-heroicons-light-bulb"
          color="amber"
          variant="soft"
          title="使用例"
          description="「事故」「水没」「改造」「業者」など"
          class="mt-2"
        />

        <!-- Add Keyword Section -->
        <div class="mt-6">
          <h5 class="text-lg font-semibold mb-3">キーワードを追加</h5>
          
          <div class="w-full flex gap-3">
            <UInput
              v-model="newKeyword"
              placeholder="除外したいキーワードを入力"
              size="lg"
              icon="i-heroicons-magnifying-glass"
              @keyup.enter="addKeyword"
              :disabled="adding"
              class="flex-1"
            />
            <UButton
              :disabled="!newKeyword?.trim() || adding"
              :loading="adding"
              @click="addKeyword"
              size="lg"
              icon="i-heroicons-plus"
              class="px-6"
            >
              {{ adding ? '追加中...' : '追加' }}
            </UButton>
          </div>
        </div>

        <!-- Current Keywords Section -->
        <div class="mt-8">
          <div class="flex items-center justify-between mb-4">
            <h5 class="text-lg font-semibold">登録済みキーワード</h5>
            <UBadge v-if="blacklist?.length" variant="soft" color="gray">
              {{ blacklist.length }}件
            </UBadge>
          </div>

          <!-- Loading state -->
          <template v-if="pending">
            <div class="space-y-3">
              <USkeleton class="h-12 w-full rounded-lg" v-for="i in 3" :key="i" />
            </div>
          </template>

          <!-- Empty state -->
          <template v-else-if="!blacklist || blacklist.length === 0">
            <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <UIcon name="i-heroicons-queue-list" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h6 class="text-lg font-medium text-gray-600 mb-2">
                キーワードが登録されていません
              </h6>
              <p class="text-gray-500 mb-4">
                上記の入力欄からブラックリストキーワードを追加してください
              </p>
              <p class="text-sm text-gray-400">
                例：「事故」「水没」「改造」など
              </p>
            </div>
          </template>

          <!-- Keywords list -->
          <template v-else>
            <p class="text-sm text-gray-600 mb-4">
              キーワードをクリックすると削除できます
            </p>
            <div class="grid gap-2" v-auto-animate>
              <!-- Using the original UModal pattern that worked -->
              <UModal
                v-for="entry in blacklist"
                :key="entry.id"
                title="キーワードを削除しますか？"
              >
                <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-red-300 hover:bg-red-50 transition-colors group">
                  <div class="flex items-center gap-3">
                    <UIcon name="i-heroicons-x-circle" class="h-5 w-5 text-red-500" />
                    <span class="font-medium text-gray-900">{{ entry.value }}</span>
                  </div>
                  <UIcon name="i-heroicons-trash" class="h-5 w-5 text-red-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>

                <template #body>
                  <div class="p-4">
                    <p class="text-gray-600 mb-4">
                      キーワード「<strong>{{ entry.value }}</strong>」を削除しますか？
                      <br>
                      <span class="text-sm text-gray-500">この操作は取り消せません。</span>
                    </p>
                    <UButton
                      variant="subtle"
                      color="error"
                      block
                      @click="removeKeyword(entry.id)"
                      :loading="deleting"
                    >
                      {{ deleting ? '削除中...' : '削除する' }}
                    </UButton>
                  </div>
                </template>
              </UModal>
            </div>
          </template>
        </div>
      </div>
      
      <!-- Bottom padding -->
      <div class="pb-16"></div>
    </template>
  </UDashboardPanel>
</template>

<script lang="ts" setup>
const toast = useToast();
const { dFetch, useDirectusFetch } = useDirectus();
const newKeyword = ref("");
const adding = ref(false);
const deleting = ref(false);

const { data: blacklist, refresh, pending } = await useDirectusFetch<Blacklist[]>(
  "/items/blacklist",
  {
    params: {
      limit: -1,
    },
  }
);

const addKeyword = async () => {
  if (!newKeyword.value?.trim()) return;
  
  adding.value = true;
  try {
    await dFetch("/items/blacklist", {
      method: "POST",
      body: {
        type: "car_name_pattern",
        value: newKeyword.value.trim(),
      },
    });
    await refresh();
    toast.add({
      title: "キーワードを追加しました",
      description: `「${newKeyword.value.trim()}」を除外リストに追加`,
      color: "success",
    });
    newKeyword.value = "";
  } catch (error) {
    console.error("Failed to add keyword:", error);
    toast.add({
      title: "キーワードの追加に失敗しました",
      description: error.message,
      color: "error",
    });
  } finally {
    adding.value = false;
  }
};

const removeKeyword = async (id: string) => {
  deleting.value = true;
  try {
    await dFetch(`/items/blacklist/${id}`, {
      method: "DELETE",
    });
    await refresh();
    toast.add({
      title: "キーワードを削除しました",
      color: "success",
    });
  } catch (error) {
    console.error("Failed to remove keyword:", error);
    toast.add({
      title: "キーワードの削除に失敗しました",
      description: error.message,
      color: "error",
    });
  } finally {
    deleting.value = false;
  }
};
</script>

<style></style>
