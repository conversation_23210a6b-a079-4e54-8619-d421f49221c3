<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title=" 🔧 中古車販売 - デバッグダッシュボード">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div>
        <UCard class="w-full border-none">

          <!-- Overall Status -->
          <div class="mb-8">
            <div class="flex items-center justify-center mb-4">
              <UBadge
                :color="overallStatus.badgeColor as any"
                variant="solid"
                size="lg"
                class="px-4 py-2"
              >
                <div class="flex items-center space-x-2">
                  <UIcon :name="overallStatus.icon" class="w-4 h-4" />
                  <span class="font-semibold">{{ overallStatus.text }}</span>
                </div>
              </UBadge>
            </div>
          </div>

          <!-- Health Checks Grid -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
          >
            <!-- Database Health -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold flex items-center gap-2">
                    <UIcon name="i-heroicons-server" class="w-5 h-5" />
                    データベース
                  </h3>
                  <UBadge
                    :color="dbHealth.status === 'healthy' ? 'success' : 'error'"
                    variant="solid"
                    size="xs"
                  />
                </div>
              </template>

              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span>ステータス:</span>
                  <UBadge
                    :color="dbHealth.status === 'healthy' ? 'success' : 'error'"
                    variant="soft"
                    size="xs"
                  >
                    {{
                      dbHealth.status === "healthy"
                        ? "正常"
                        : dbHealth.status === "unhealthy"
                        ? "異常"
                        : "確認中..."
                    }}
                  </UBadge>
                </div>

                <div
                  v-if="dbHealth.connected !== undefined"
                  class="flex justify-between"
                >
                  <span>接続状態:</span>
                  <UBadge
                    :color="dbHealth.connected ? 'success' : 'error'"
                    variant="soft"
                    size="xs"
                  >
                    {{ dbHealth.connected ? "接続済み" : "未接続" }}
                  </UBadge>
                </div>
                <div v-if="dbHealth.timestamp" class="flex justify-between">
                  <span>サーバー時刻:</span>
                  <span class="font-mono text-xs text-gray-600">{{
                    formatTime(dbHealth.timestamp)
                  }}</span>
                </div>
                <div v-if="dbHealth.error">
                  <UAlert
                    color="error"
                    variant="soft"
                    :title="dbHealth.error"
                  />
                </div>
              </div>
            </UCard>

            <!-- Database Stats -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold flex items-center gap-2">
                    <UIcon name="i-heroicons-chart-bar" class="w-5 h-5" />
                    データ統計
                  </h3>
                  <UBadge
                    :color="dbStats.success ? 'success' : 'error'"
                    variant="solid"
                    size="xs"
                  />
                </div>
              </template>

              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span>総車両数:</span>
                  <span class="font-mono font-bold">{{
                    dbStats.totalCars ?? "..."
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span>最近の車両:</span>
                  <span class="font-mono">{{
                    dbStats.recentCars ?? "..."
                  }}</span>
                </div>
                <div v-if="dbStats.error">
                  <UAlert color="error" variant="soft" :title="dbStats.error" />
                </div>
              </div>
            </UCard>

            <!-- Scraper Status -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold flex items-center gap-2">
                    <UIcon name="i-heroicons-cog-6-tooth" class="w-5 h-5" />
                    スクレイパー
                  </h3>
                  <UBadge
                    :color="scraperStatus.isRunning ? 'warning' : 'success'"
                    variant="solid"
                    size="xs"
                  />
                </div>
              </template>

              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span>ステータス:</span>
                  <UBadge
                    :color="scraperStatus.isRunning ? 'warning' : 'success'"
                    variant="soft"
                    size="xs"
                  >
                    {{ scraperStatus.isRunning ? "実行中" : "待機中" }}
                  </UBadge>
                </div>
                <div v-if="scraperStatus.lastRun" class="flex justify-between">
                  <span>最終実行:</span>
                  <span class="font-mono text-xs text-gray-600">{{
                    formatTime(scraperStatus.lastRun)
                  }}</span>
                </div>
                <div v-if="!scraperStatus.lastRun" class="flex justify-between">
                  <span>最終実行:</span>
                  <span class="font-mono text-gray-500 text-xs">未実行</span>
                </div>
                <!-- Current scheduling -->
                <div class="flex justify-between">
                  <span>スケジュール:</span>
                  <span class="font-mono text-xs text-gray-600">{{
                    $config.public.scraperScheduleCron
                  }}</span>
                </div>
                <div v-if="scraperStatus.error">
                  <UAlert
                    color="error"
                    variant="soft"
                    :title="scraperStatus.error"
                  />
                </div>
              </div>
            </UCard>

            <!-- Directus Status -->
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold flex items-center gap-2">
                    <UIcon
                      name="i-heroicons-adjustments-horizontal"
                      class="w-5 h-5"
                    />
                    Directus
                  </h3>
                  <UBadge
                    :color="directusStatus.accessible ? 'success' : 'error'"
                    variant="solid"
                    size="xs"
                  />
                </div>
              </template>

              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span>ステータス:</span>
                  <UBadge
                    :color="directusStatus.accessible ? 'success' : 'error'"
                    variant="soft"
                    size="xs"
                  >
                    {{ directusStatus.accessible ? "利用可能" : "利用不可" }}
                  </UBadge>
                </div>
                <div v-if="directusStatus.version" class="flex justify-between">
                  <span>バージョン:</span>
                  <span class="font-mono text-xs text-gray-600">{{
                    directusStatus.version
                  }}</span>
                </div>
                <div v-if="directusStatus.cache" class="flex justify-between">
                  <span>キャッシュ:</span>
                  <UBadge color="success" variant="soft" size="xs">
                    5分
                  </UBadge>
                </div>
                <div v-if="directusStatus.error">
                  <UAlert
                    color="error"
                    variant="soft"
                    :title="directusStatus.error"
                  />
                </div>
              </div>
            </UCard>
          </div>

          <template #footer>
            <div class="space-y-4">
              <!-- Actions -->
              <div>
                <h3
                  class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
                >
                  <UIcon name="i-heroicons-rocket-launch" class="w-5 h-5" />
                  クイックアクション
                </h3>
                <div class="flex flex-wrap gap-3">
                  <UButton
                    @click="triggerScraper"
                    :loading="isTriggering"
                    color="primary"
                    icon="i-heroicons-play"
                  >
                    {{ isTriggering ? "開始中..." : "スクレイパーを起動" }}
                  </UButton>

                  <UButton
                    @click="refreshAll"
                    :disabled="isRefreshing"
                    :loading="isRefreshing"
                    color="success"
                    variant="outline"
                    icon="i-heroicons-arrow-path"
                  >
                    {{ isRefreshing ? "更新中..." : "全体を更新" }}
                  </UButton>
                </div>
              </div>

              <!-- Footer -->
              <div class="text-center text-sm text-gray-500 pt-4 border-t">
                <div class="flex items-center justify-center gap-2">
                  <UIcon name="i-heroicons-clock" class="w-4 h-4" />
                  最終更新: {{ lastUpdated }}
                </div>
              </div>
            </div>
          </template>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script lang="ts" setup>
interface DbHealth {
  status?: string;
  connected?: boolean;
  timestamp?: string;
  error?: string;
}

interface DbStats {
  success: boolean;
  totalCars?: number;
  recentCars?: number;
  error?: string;
}

interface ScraperStatus {
  isRunning: boolean;
  lastRun?: string | null;
  error?: string | null;
}

interface DirectusStatus {
  accessible: boolean;
  version?: string;
  uptime?: number | null;
  cache?: {
    enabled: boolean;
    store: string;
    ttl: string;
  };
  error?: string;
}

// Reactive state
const dbHealth = ref<DbHealth>({});
const dbStats = ref<DbStats>({ success: false });
const scraperStatus = ref<ScraperStatus>({ isRunning: false });
const directusStatus = ref<DirectusStatus>({ accessible: false });
const lastUpdated = ref<string>("");
const isRefreshing = ref(false);
const isTriggering = ref(false);

// Computed overall status
const overallStatus = computed(() => {
  const isDbHealthy = dbHealth.value.status === "healthy";
  const isDbConnected = dbHealth.value.connected === true;
  const isDirectusOk = directusStatus.value.accessible;

  if (isDbHealthy && isDbConnected && isDirectusOk) {
    return {
      badgeColor: "success",
      text: "すべてのシステムが正常に稼働しています",
      icon: "i-heroicons-check-circle",
    };
  } else if (isDbHealthy && isDbConnected) {
    return {
      badgeColor: "warning",
      text: "一部サービスが利用可能です",
      icon: "i-heroicons-exclamation-triangle",
    };
  } else {
    return {
      badgeColor: "error",
      text: "サービスに問題が検出されました",
      icon: "i-heroicons-x-circle",
    };
  }
});

// Helper function to format timestamps
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString();
};

// Check database health
const checkDatabaseHealth = async () => {
  try {
    const response = await $fetch("/api/database/health");
    if (response.success) {
      dbHealth.value = response.health;
    } else {
      dbHealth.value = { status: "unhealthy", error: "Health check failed" };
    }
  } catch (error) {
    dbHealth.value = {
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Check database stats
const checkDatabaseStats = async () => {
  try {
    const response = await $fetch("/api/database/stats");
    if (response.success) {
      dbStats.value = {
        success: true,
        totalCars: response.stats.totalCars,
        recentCars: response.stats.recentCars,
      };
    } else {
      dbStats.value = { success: false, error: "Stats check failed" };
    }
  } catch (error) {
    dbStats.value = {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Check scraper status
const checkScraperStatus = async () => {
  try {
    const response = await $fetch("/api/scraper/status");
    if (response.success) {
      scraperStatus.value = {
        isRunning: response.status.isRunning,
        lastRun: response.status.lastRun,
        error: response.status.error,
      };
    } else {
      scraperStatus.value = { isRunning: false, error: "Status check failed" };
    }
  } catch (error) {
    scraperStatus.value = {
      isRunning: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Check Directus accessibility
const checkDirectusStatus = async () => {
  try {
    const response = await $fetch("/api/directus/health");
    if (response.success) {
      directusStatus.value = response.status;
    } else {
      directusStatus.value = response.status;
    }
  } catch (error) {
    directusStatus.value = {
      accessible: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

const toast = useToast();

// Trigger scraper
const triggerScraper = async () => {
  if (scraperStatus.value.isRunning || isTriggering.value) {
    toast.add({
      title: "スクレイパーは既に実行中です",
      description: "現在のスクレイピング作業が完了するまでお待ちください",
      color: "warning",
      timeout: 5000
    });
    return;
  }

  isTriggering.value = true;
  try {
    const response = await $fetch("/api/scraper/start", {
      method: "POST",
      body: { outputMode: "database" },
    });

    if (response.success) {
      // Refresh scraper status after a short delay
      setTimeout(() => {
        checkScraperStatus();
      }, 1000);
    }
  } catch (error) {
    console.error("Failed to trigger scraper:", error);
  } finally {
    isTriggering.value = false;
  }
};

// Refresh all status checks
const refreshAll = async () => {
  isRefreshing.value = true;
  try {
    await Promise.all([
      checkDatabaseHealth(),
      checkDatabaseStats(),
      checkScraperStatus(),
      checkDirectusStatus(),
    ]);
    lastUpdated.value = new Date().toLocaleString();
  } finally {
    isRefreshing.value = false;
  }
};

// Initial load and periodic refresh
onMounted(async () => {
  await refreshAll();

  // Auto-refresh every 30 seconds
  setInterval(refreshAll, 30000);
});
</script>
