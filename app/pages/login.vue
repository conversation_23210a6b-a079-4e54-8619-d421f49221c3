<script setup lang="ts">
import * as z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";

const toast = useToast();
const { login, leftOffUrl } = useUser();

const fields = [
  {
    name: "email",
    type: "text" as const,
    label: "eメール",
    placeholder: "メールアドレスを入力してください",
    required: true,
    size: "xl" as const,
  },
  {
    name: "password",
    label: "パスワード",
    type: "password" as const,
    placeholder: "パスワードを入力してください",
    size: 'xl' as const
  },
];

// const providers = [{
//   label: 'Google',
//   icon: 'i-simple-icons-google',
//   onClick: () => {
//     toast.add({ title: 'Google', description: 'Login with Google' })
//   }
// }, {
//   label: 'GitHub',
//   icon: 'i-simple-icons-github',
//   onClick: () => {
//     toast.add({ title: 'GitHub', description: 'Login with GitHub' })
//   }
// }]

const isLoading = ref(false);

const schema = z.object({
  email: z.string().email("メールアドレスを入力してください"),
  password: z.string().min(8, "パスワードは8文字以上で入力してください"),
});

type Schema = z.output<typeof schema>;

async function onSubmit(payload: FormSubmitEvent<Schema>) {
  isLoading.value = true;
  await login(payload.data.email, payload.data.password);
  isLoading.value = false;
  if (leftOffUrl.value) {
    navigateTo(leftOffUrl.value);
  } else {
    navigateTo("/");
  }
}

async function demoLogin() {
  await onSubmit({
    data: {
      email: "<EMAIL>",
      password: "admin123",
    },
  } as FormSubmitEvent<Schema>);
}
</script>

<template>
  <div class="w-full flex items-center justify-center min-h-screen p-4">
    <UCard class="w-full max-w-md pt-3">
      <UAuthForm
        :schema="schema"
        :fields="fields"
        title="ログイン"
        icon="i-lucide-lock"
        :loading="isLoading"
        :submit="{
          label: 'ログイン',
          size: 'xl',
        }"
        @submit="onSubmit"
      >
      </UAuthForm>
      <DevOnly>
        <UButton id="demo-login" @click="demoLogin" color="error" block class="mt-2" size="xl">
          Demo Login
        </UButton>
      </DevOnly>
    </UCard>
  </div>
</template>
