<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title="設定">
        <template #leading>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div>
        <h2 class="text-3xl font-bold">ブラックリスト管理</h2>
        <p class="text-muted mt-2 mb-6">
          検索結果から除外するメーカーやモデルを管理します
        </p>

        <!-- Blacklist table with type-based columns -->
        <div class="grid grid-cols-2 gap-4 h-[70vh]">
          <!-- Left side: Maker blacklist -->
          <div
            class="flex flex-col border border-gray-200 rounded overflow-hidden"
          >
            <!-- Fixed header -->
            <div class="bg-gray-50 border-b border-gray-200 p-3 font-medium">
              メーカー ブラックリスト ({{ makerBlacklist.length }})
            </div>

            <!-- Scrollable content -->
            <div class="flex-1 overflow-y-auto">
              <div v-if="isLoading" class="p-4 text-gray-500 text-center">
                データを読み込んでいます...
              </div>
              <div
                v-else-if="makerBlacklist.length === 0"
                class="p-4 text-gray-500 text-center"
              >
                ブラックリストに登録されたメーカーはありません
              </div>
              <div v-else class="divide-y divide-gray-100">
                <div
                  v-for="item in makerBlacklist"
                  :key="item.id"
                  class="p-3 hover:bg-gray-50 flex items-center justify-between"
                >
                  <span class="text-sm">{{
                    MAKERS.find((maker) => maker.slug === item.value)?.nameJa
                  }}</span>
                  <UModal title="クロール対象に追加しますか？">
                    <UButton
                      icon="i-heroicons-trash"
                      class="cursor-pointer"
                      color="success"
                    />
                    <template #body>
                      <p>
                        メーカー「<strong>{{
                          MAKERS.find((maker) => maker.slug === item.value)
                            ?.nameJa
                        }}</strong
                        >」をクロール対象に戻しますか？
                      </p>
                      <UButton
                        @click="removeFromBlacklist(item)"
                        color="success"
                        block
                        class="mt-4"
                        >はい</UButton
                      >
                    </template>
                  </UModal>
                </div>
              </div>
            </div>
          </div>

          <!-- Right side: Model blacklist -->
          <div
            class="flex flex-col border border-gray-200 rounded overflow-hidden"
          >
            <!-- Fixed header -->
            <div class="bg-gray-50 border-b border-gray-200 p-3 font-medium">
              モデル ブラックリスト ({{ modelBlacklist.length }})
            </div>

            <!-- Scrollable content -->
            <div class="flex-1 overflow-y-auto">
              <div v-if="isLoading" class="p-4 text-gray-500 text-center">
                データを読み込んでいます...
              </div>
              <div
                v-else-if="modelBlacklist.length === 0"
                class="p-4 text-gray-500 text-center"
              >
                ブラックリストに登録されたモデルはありません
              </div>
              <div v-else class="divide-y divide-gray-100">
                <div
                  v-for="item in modelBlacklist"
                  :key="item.id"
                  class="p-3 hover:bg-gray-50 flex items-center justify-between"
                >
                  <span class="text-sm">{{ item.value }}</span>
                  <UModal title="クロール対象に追加しますか？">
                    <UButton icon="i-heroicons-trash" color="success" class="" />
                    <template #body>
                      <p>
                        モデル「<strong>{{ item.value }}</strong
                        >」をクロール対象に戻しますか？
                      </p>
                      <UButton
                        @click="removeFromBlacklist(item)"
                        color="success"
                        block
                        class="mt-4"
                        >はい</UButton
                      >
                    </template>
                  </UModal>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script lang="ts" setup>
interface BlacklistItem {
  id: string;
  type: string;
  value: string;
  is_active: boolean;
}

const { dFetch } = useDirectus();
const toast = useToast();

// Data
const isLoading = ref(false);
const blacklistItems = ref<BlacklistItem[]>([]);

// Computed
const makerBlacklist = computed(() =>
  blacklistItems.value.filter((item) => item.type === "maker")
);

const modelBlacklist = computed(() =>
  blacklistItems.value.filter((item) => item.type === "model")
);

// Methods
const loadBlacklist = async () => {
  try {
    isLoading.value = true;
    const data = await dFetch<BlacklistItem[]>("/items/blacklist", {
      params: {
        fields: "id,type,value,is_active",
        filter: {
          is_active: { _eq: true },
        },
        limit: -1,
      },
    });
    blacklistItems.value = data || [];
  } catch (error) {
    console.error("Failed to load blacklist:", error);
    toast.add({
      title: "エラー",
      description: "ブラックリストの読み込みに失敗しました",
      color: "error",
    });
  } finally {
    isLoading.value = false;
  }
};

const removeFromBlacklist = async (item: BlacklistItem) => {
  try {
    await dFetch(`/items/blacklist/${item.id}`, {
      method: "DELETE",
    });

    toast.add({
      title: "成功",
      description: "ブラックリストから削除しました",
      color: "success",
    });

    await loadBlacklist();
  } catch (error) {
    console.error("Failed to remove from blacklist:", error);
    toast.add({
      title: "エラー",
      description: "削除に失敗しました",
      color: "error",
    });
  }
};

// Load data on mount
onMounted(() => {
  loadBlacklist();
});
</script>

<style></style>
