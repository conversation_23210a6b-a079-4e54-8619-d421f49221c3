<script setup lang="ts">
const { dFetch } = useDirectus();
const toast = useToast();
const { blacklistedMakers, blacklistedModels, getBlacklists } = useBlacklist();
await getBlacklists();

const blacklistedMakerKeywords = computed(() => {
  // Join all keywords with memoization
  const makers = blacklistedMakers.value;
  if (!makers.length) return [];
  return makers.reduce((acc, maker) => {
    if (maker.keywords?.length) {
      acc.push(...maker.keywords);
    }
    return acc;
  }, []);
});

const isEditingMenu = ref(false);
const selectedForBlacklist = ref<{
  makers: string[]; // maker slugs
  models: string[]; // model names
}>({
  makers: [],
  models: [],
});

const japaneseMakers = computed(() => {
  // Cache blacklisted keywords and search term
  const blacklisted = blacklistedMakerKeywords.value;
  const search = searchMaker.value.toLowerCase();

  // Early return for empty blacklist
  const blacklistSet = blacklisted.length > 0 ? new Set(blacklisted) : null;

  // Filter once with all conditions
  return MAKERS.filter((maker) => {
    // Country filter first (cheapest)
    if (maker.country !== "JP") return false;

    // Blacklist filter (use Set for O(1) lookup)
    if (blacklistSet && blacklistSet.has(maker.nameJa)) return false;

    // Search filter (most expensive, do last)
    if (search === "") return true;
    return (
      maker.nameJa.toLowerCase().includes(search) ||
      maker.name.toLowerCase().includes(search)
    );
  });
});

const foreignMakers = computed(() => {
  // Cache blacklisted keywords and search term
  const blacklisted = blacklistedMakerKeywords.value;
  const search = searchMaker.value.toLowerCase();

  // Early return for empty blacklist
  const blacklistSet = blacklisted.length > 0 ? new Set(blacklisted) : null;

  // Filter once with all conditions
  return MAKERS.filter((maker) => {
    // Country filter first (cheapest)
    if (maker.country === "JP") return false;

    // Blacklist filter (use Set for O(1) lookup)  
    if (blacklistSet && blacklistSet.has(maker.nameJa)) return false;

    // Search filter (most expensive, do last)
    if (search === "") return true;
    return (
      maker.nameJa.toLowerCase().includes(search) ||
      maker.name.toLowerCase().includes(search)
    );
  });
});

type Maker = (typeof MAKERS)[0];

const { homeMakerSelected, homeMakerPosition, homeModelPosition } =
  useScrollPosition();
const searchMaker = ref("");
const isLoading = ref(false);
const models = ref<{ maker: string; model: string; count: number }[]>([]);

const buildInitialFilter = () => {
  const filters = [];

  if (blacklistedMakerKeywords.value.length > 0) {
    filters.push({
      maker: {
        _nin: blacklistedMakerKeywords.value,
      },
    });
  }

  if (blacklistedModels.value.length > 0) {
    filters.push({
      model: {
        _nin: blacklistedModels.value,
      },
    });
  }

  return filters.length > 0 ? { _and: filters } : {};
};

const initialFilter = buildInitialFilter();

const allModels = await dFetch<
  { maker: string; model: string; count: number }[]
>("/items/used_cars", {
  server: false,
  params: {
    aggregate: {
      count: "*",
    },
    sort: "model",
    groupBy: "maker,model",
    limit: -1,
    ...(Object.keys(initialFilter).length > 0 && { filter: initialFilter }),
  },
});
models.value = allModels;

const getModels = async (maker: Maker) => {
  isLoading.value = true;
  homeMakerSelected.value = maker;

  const filters = [
    {
      maker: {
        _in: maker.keywords,
      },
    },
  ];

  if (blacklistedMakerKeywords.value.length > 0) {
    filters.push({
      maker: {
        _nin: blacklistedMakerKeywords.value,
      },
    });
  }

  if (blacklistedModels.value.length > 0) {
    filters.push({
      model: {
        _nin: blacklistedModels.value,
      },
    });
  }

  const data = await dFetch<{ maker: string; model: string; count: number }[]>(
    "/items/used_cars",
    {
      params: {
        filter: {
          _and: filters,
        },
        aggregate: {
          count: "*",
        },
        sort: "model",
        groupBy: "maker,model",
        limit: -1,
      },
    }
  );
  isLoading.value = false;
  models.value = data;

  // Restore model list scroll position after data loads
  nextTick(() => {
    setTimeout(() => {
      if (modelsScrollEl.value && homeModelPosition.value > 0) {
        modelsScrollEl.value.scrollTop = homeModelPosition.value;
      }
    }, 50);
  });
};

const makersScrollEl = ref<HTMLElement>();
const modelsScrollEl = ref<HTMLElement>();

const handleMakerScroll = () => {
  if (makersScrollEl.value) {
    homeMakerPosition.value = makersScrollEl.value.scrollTop;
  }
};

const handleModelScroll = () => {
  if (modelsScrollEl.value) {
    homeModelPosition.value = modelsScrollEl.value.scrollTop;
  }
};

const restoreScrollPositions = () => {
  nextTick(() => {
    setTimeout(() => {
      if (makersScrollEl.value && homeMakerPosition.value > 0) {
        makersScrollEl.value.scrollTop = homeMakerPosition.value;
      }
      if (modelsScrollEl.value && homeModelPosition.value > 0) {
        modelsScrollEl.value.scrollTop = homeModelPosition.value;
      }
    }, 100);
  });
};

onActivated(async () => {
  // If a maker was previously selected, reload its models
  if (homeMakerSelected.value && !isLoading.value) {
    await getModels(homeMakerSelected.value);
  }
  restoreScrollPositions();
});

onMounted(async () => {
  // If a maker was previously selected, reload its models
  if (homeMakerSelected.value && !isLoading.value) {
    await getModels(homeMakerSelected.value);
  }
  restoreScrollPositions();
});

const toggleMakerSelection = (maker: Maker) => {
  const index = selectedForBlacklist.value.makers.indexOf(maker.slug);
  if (index > -1) {
    selectedForBlacklist.value.makers.splice(index, 1);
  } else {
    selectedForBlacklist.value.makers.push(maker.slug);
  }
};

const toggleModelSelection = (model: { maker: string; model: string }) => {
  const index = selectedForBlacklist.value.models.indexOf(model.model);
  if (index > -1) {
    selectedForBlacklist.value.models.splice(index, 1);
  } else {
    selectedForBlacklist.value.models.push(model.model);
  }
};

const saveChanges = async () => {
  isLoading.value = true;
  try {
    // Add selected makers to blacklist
    for (const makerSlug of selectedForBlacklist.value.makers) {
      await dFetch("/items/blacklist", {
        method: "POST",
        body: {
          type: "maker",
          value: makerSlug,
          is_active: true,
        },
      });
    }

    // Add selected models to blacklist
    for (const modelName of selectedForBlacklist.value.models) {
      await dFetch("/items/blacklist", {
        method: "POST",
        body: {
          type: "model",
          value: modelName,
          is_active: true,
        },
      });
    }

    toast.add({
      title: "変更を保存しました",
      duration: 2000,
      description: `${
        selectedForBlacklist.value.makers.length +
        selectedForBlacklist.value.models.length
      }件の項目をブラックリストに追加しました。ページが再読み込みされます。`,
      color: "success",
    });

    // Clear SessionStorage
    sessionStorage.removeItem("homeMakerSelected");
    sessionStorage.removeItem("homeMakerPosition");
    sessionStorage.removeItem("homeModelPosition");

    // Reset selection and refresh page
    selectedForBlacklist.value.makers = [];
    selectedForBlacklist.value.models = [];

    // Delay 2 seonds
    await new Promise((resolve) => setTimeout(resolve, 2500));

    // Refresh the page to reload data
    window.location.reload();
  } catch (error) {
    console.error("Failed to save changes:", error);
    toast.add({
      title: "変更の保存に失敗しました",
      description: error instanceof Error ? error.message : "Unknown error",
      color: "error",
    });
  }
};

const closeAndRefresh = () => {
  selectedForBlacklist.value.makers = [];
  selectedForBlacklist.value.models = [];
  isEditingMenu.value = false;
};
</script>

<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title="検索">
        <template #leading>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div>
        <h2 class="text-3xl font-bold">メーカー車名選択</h2>
        <div class="flex items-end">
          <div>
            <h3 class="text-muted">
              メーカー車名選択 メーカー、車名選択を選択してください
            </h3>
            <p v-if="isEditingMenu" class="text-sm text-error">
              選択したメーカーと車名をブラックリストに追加します
            </p>
            <p v-if="isEditingMenu" class="text-sm text-error">
              変更を保存するには、「変更を保存」ボタンをクリックしてください
            </p>
            <p v-if="isEditingMenu" class="text-sm text-error">
              ブラックリストに追加されたメーカーおよび車名は検索結果に表示されません
            </p>

          </div>

          <UButton
            v-if="!isEditingMenu"
            @click="isEditingMenu = !isEditingMenu"
            variant="ghost"
            color="neutral"
            icon="i-heroicons-cog-6-tooth"
            class="ml-auto cursor-pointer"
          >
            クロール管理
          </UButton>
          <UButton
            v-else
            @click="saveChanges"
            color="success"
            icon="i-heroicons-check"
            :loading="isLoading"
            class="ml-auto cursor-pointer"
          >
            <!-- save changes  in JP-->
            変更を保存
          </UButton>
        </div>

        <!-- Makers and Models list -->
        <div class="mt-4 grid grid-cols-2 gap-4 h-[70vh]">
          <!-- Left side: Makers list -->
          <div
            id="makers"
            class="flex flex-col border border-gray-200 rounded overflow-hidden"
          >
            <!-- Fixed header -->
            <div
              class="bg-gray-50 border-b border-gray-200 p-3 font-medium flex items-center"
            >
              <p>メーカー選択</p>
              <UInput
                v-model="searchMaker"
                placeholder="メーカーを検索"
                class="ml-auto"
              />
            </div>

            <!-- Scrollable content -->
            <div
              ref="makersScrollEl"
              class="flex-1 overflow-y-auto"
              v-auto-animate
              @scroll="handleMakerScroll"
            >
              <!-- All makers option -->
              <UButton
                v-if="!isEditingMenu"
                block
                variant="ghost"
                @click="!isEditingMenu && (homeMakerSelected = null)"
                class="h-12 rounded-none justify-start text-black cursor-pointer border-b border-gray-100 hover:bg-gray-50"
              >
                すべてのメーカー
              </UButton>

              <!-- Japanese makers section -->
              <div
                class="sticky top-0 bg-white border-b border-gray-200 p-3 font-medium text-sm z-10"
              >
                国産車
              </div>
              <div class="divide-y divide-gray-100">
                <UButton
                  v-for="maker in japaneseMakers"
                  :key="maker.slug"
                  block
                  variant="ghost"
                  class="h-14 px-3 rounded-none justify-start text-black cursor-pointer hover:bg-info-100"
                  :class="{
                    'bg-info-50': homeMakerSelected?.slug === maker.slug,
                    'bg-red-100':
                      isEditingMenu &&
                      selectedForBlacklist.makers.includes(maker.slug),
                  }"
                  @click="!isEditingMenu && getModels(maker)"
                >
                  <div class="flex items-center w-full gap-3">
                    <img
                      :src="maker.image.localThumb"
                      :alt="maker.nameJa"
                      class="w-10 h-10 rounded object-contain flex-shrink-0"
                    />
                    <span class="text-sm">{{ maker.nameJa }}</span>

                    <!-- Minus icon when isEditingMenu open. If click add Maker to blacklist and grey out -->
                    <UButton
                      v-if="isEditingMenu"
                      :leading-icon="
                        selectedForBlacklist.makers.includes(maker.slug)
                          ? 'i-heroicons-plus'
                          : 'i-heroicons-minus'
                      "
                      variant="outline"
                      :color="
                        selectedForBlacklist.makers.includes(maker.slug)
                          ? 'success'
                          : 'error'
                      "
                      class="ml-auto cursor-pointer"
                      @click="toggleMakerSelection(maker)"
                    />
                  </div>
                </UButton>
              </div>

              <!-- Foreign makers section -->
              <div
                class="sticky top-0 bg-white border-b border-gray-200 p-3 font-medium text-sm  z-10"
              >
                輸入車
              </div>
              <div class="divide-y divide-gray-100">
                <UButton
                  v-for="maker in foreignMakers"
                  :key="maker.slug"
                  block
                  variant="ghost"
                  class="h-14 px-3 rounded-none justify-start text-black cursor-pointer hover:bg-info-100"
                  :class="{
                    'bg-info-50': homeMakerSelected?.slug === maker.slug,
                    'bg-red-100':
                      isEditingMenu &&
                      selectedForBlacklist.makers.includes(maker.slug),
                  }"
                  @click="!isEditingMenu && getModels(maker)"
                >
                  <div class="flex items-center w-full gap-3">
                    <img
                      :src="maker.image.localThumb"
                      :alt="maker.nameJa"
                      class="w-10 h-10 rounded object-contain flex-shrink-0"
                    />
                    <span class="text-sm">{{ maker.nameJa }}</span>
                    <UButton
                      v-if="isEditingMenu"
                      :leading-icon="
                        selectedForBlacklist.makers.includes(maker.slug)
                          ? 'i-heroicons-plus'
                          : 'i-heroicons-minus'
                      "
                      variant="outline"
                      :color="
                        selectedForBlacklist.makers.includes(maker.slug)
                          ? 'success'
                          : 'error'
                      "
                      class="ml-auto cursor-pointer"
                      @click="toggleMakerSelection(maker)"
                    />
                  </div>
                </UButton>
              </div>
            </div>
          </div>

          <!-- Right side: Models list -->
          <div
            id="models"
            class="flex flex-col border border-gray-200 rounded overflow-hidden"
            :class="{ 'animate-pulse': isLoading }"
          >
            <!-- Fixed header -->
            <div class="bg-gray-50 border-b border-gray-200 font-medium p-4">
              モデル
            </div>

            <!-- Scrollable content -->
            <div
              ref="modelsScrollEl"
              class="flex-1 overflow-y-auto"
              @scroll="handleModelScroll"
            >
              <div class="divide-y divide-gray-100">
                <!-- If no models -->
                <!-- <div
                  v-if="models?.length === 0"
                  class="p-4 text-gray-500 text-center"
                >
                  メーカーを選択してください
                </div> -->

                <!-- All models of selected maker -->
                <nuxt-link
                  v-if="!isEditingMenu"
                  :to="{
                    name: 'cars',
                    query: {
                      maker: homeMakerSelected
                        ? homeMakerSelected.nameJa
                        : undefined,
                    },
                  }"
                  variant="ghost"
                  class="block p-3 rounded-none justify-start text-black cursor-pointer hover:bg-gray-50"
                >
                  <span class="text-sm">すべての車名</span>
                </nuxt-link>
                <!-- <div
                  v-if="!homeMakerSelected && isEditingMenu"
                  class="block p-3 rounded-none justify-start text-gray-400 cursor-not-allowed"
                >
                  <span class="text-sm">すべての車名</span>
                </div> -->

                <!-- Models -->
                <!-- ifloading -->
                <div v-if="isLoading" class="p-4 text-gray-500 text-center">
                  データを読み込んでいます...
                </div>
                <nuxt-link
                  v-if="!isLoading && !isEditingMenu"
                  v-for="model in models"
                  :key="model.model"
                  variant="ghost"
                  class="flex items-center p-3 rounded-none justify-start w-full text-black cursor-pointer hover:bg-gray-50"
                  :class="{ 'opacity-100': isLoading }"
                  external
                  :to="`/cars?maker=${model.maker}&model=${encodeURIComponent(
                    model.model
                  )}`"
                >
                  <span class="text-sm"
                    >{{ model.model }} ({{ model.count }})</span
                  >
                </nuxt-link>

                <div
                  v-if="!isLoading && isEditingMenu"
                  v-for="model in models"
                  :key="model.model"
                  class="flex items-center p-3 rounded-none justify-start w-full cursor-not-allowed"
                  :class="{
                    'opacity-10': isLoading,
                    'bg-red-100': selectedForBlacklist.models.includes(
                      model.model
                    ),
                    'text-gray-400': !selectedForBlacklist.models.includes(
                      model.model
                    ),
                  }"
                >
                  <span class="text-sm"
                    >{{ model.model }} ({{ model.count }})</span
                  >
                  <UButton
                    :leading-icon="
                      selectedForBlacklist.models.includes(model.model)
                        ? 'i-heroicons-plus'
                        : 'i-heroicons-minus'
                    "
                    variant="outline"
                    :color="
                      selectedForBlacklist.models.includes(model.model)
                        ? 'success'
                        : 'error'
                    "
                    class="ml-auto cursor-pointer"
                    @click="toggleModelSelection(model)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
