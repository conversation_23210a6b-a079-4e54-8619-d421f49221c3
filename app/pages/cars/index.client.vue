<script setup lang="ts">
import { watchDebounced } from "@vueuse/core";
const { useDirectusFetch } = useDirectus();
const route = useRoute("cars");
const { blacklistedMakers, blacklistedModels, getBlacklists } = useBlacklist();
await getBlacklists();

// Year range validation
const yearRangeError = ref("");

// Filter states
const searchDisabled = ref(false);
const car_name = ref("");
const maker = ref((route.query?.maker as string) ?? "");
const model = ref((route.query?.model as string) ?? "");
const region = ref("all");
const yearFrom = ref(1886);
const yearTo = ref(2005);
const priceMode = ref("all");
const priceInputMode = ref("slider");
const priceFrom = ref(0);
const priceTo = ref(10000);
const mileageFrom = ref(0);
const mileageTo = ref(1000000);
const mission = ref("");
const drivetrain = ref("");
const accident_history = ref("");
const steering = ref("");
const engine_type = ref("");
const sort = ref("scraped_desc");

const debouncedYearFrom = ref(yearFrom.value);
const debouncedYearTo = ref(yearTo.value);
const debouncedPriceFrom = ref(priceFrom.value);
const debouncedPriceTo = ref(priceTo.value);
const debouncedMileageFrom = ref(mileageFrom.value);
const debouncedMileageTo = ref(mileageTo.value);

watchDebounced([yearFrom, yearTo, priceFrom, priceTo, mileageFrom, mileageTo], ([newYearFrom, newYearTo, newPriceFrom, newPriceTo, newMileageFrom, newMileageTo]) => {
  debouncedYearFrom.value = newYearFrom;
  debouncedYearTo.value = newYearTo;
  debouncedPriceFrom.value = newPriceFrom;
  debouncedPriceTo.value = newPriceTo;
  debouncedMileageFrom.value = newMileageFrom;
  debouncedMileageTo.value = newMileageTo;
}, { debounce: 500 });

const sliderPrice = ref([0, 10000]);

watch(sliderPrice, (newVal) => {
  priceFrom.value = newVal[0] ?? 0;
  priceTo.value = newVal[1] ?? 10000;
});

watch([priceInputMode, priceFrom, priceTo], ([mode, from, to]) => {
  if (mode === "slider") {
    sliderPrice.value = [from, to];
  }
});

onMounted(() => {
  resetFilters();

  // get params from route to put into filterState
  if (route.query?.maker) {
    maker.value = route.query.maker as string;
  }
  if (route.query?.model) {
    model.value = route.query.model as string;
  }
  if (route.query?.yearFrom) {
    yearFrom.value = parseInt(route.query.yearFrom as string);
  }
  if (route.query?.yearTo) {
    yearTo.value = parseInt(route.query.yearTo as string);
  }
  if (route.query?.priceFrom) {
    priceFrom.value = Number(route.query.priceFrom);
  }
  if (route.query?.priceMode) {
    priceMode.value = route.query.priceMode as string;
  }
  if (route.query?.priceInputMode) {
    priceInputMode.value = route.query.priceInputMode as string;
  }
  if (route.query?.priceTo) {
    priceTo.value = Number(route.query.priceTo);
  }
  if (route.query?.mileageFrom) {
    mileageFrom.value = parseInt(route.query.mileageFrom as string);
  }
  if (route.query?.mileageTo) {
    mileageTo.value = parseInt(route.query.mileageTo as string);
  }
  if (route.query?.mission) {
    mission.value = route.query.mission as string;
  }
  if (route.query?.drivetrain) {
    drivetrain.value = route.query.drivetrain as string;
  }
  if (route.query?.accident_history) {
    accident_history.value = route.query.accident_history as string;
  }
  if (route.query?.steering) {
    steering.value = route.query.steering as string;
  }
  if (route.query?.engine_type) {
    engine_type.value = route.query.engine_type as string;
  }
  if (route.query?.sort) {
    sort.value = route.query.sort as string;
  }
  if (route.query?.car_name) {
    car_name.value = route.query.car_name as string;
  }
  if (route.query?.page) {
    currentPage.value = Number(route.query.page);
  }
});

const searchLimit = useCookie("searchLimit", { default: () => 100 });
const currentPage = ref(1);
const computedFilters = computed(() => {
  const filters: any = {};

  // If car_name is not empty, add it to filters
  if (car_name.value) {
    filters["car_name"] = {
      _icontains: car_name.value,
    };
  }

  // If maker is not empty, add it to filters
  // Always add blacklisted makers to filters
  if (maker.value) {
    filters["maker"] = {
      _icontains: maker.value,
    };
  }
  // Always exclude blacklisted makers (by keywords)
  const makerBlacklist = blacklistedMakers.value.flatMap((m) => m.keywords);
  if (makerBlacklist.length > 0) {
    if (!filters["maker"]) filters["maker"] = {};
    filters["maker"]["_nin"] = makerBlacklist;
  }

  // If model is not empty, add it to filters
  // Always add blacklisted models to filters
  if (model.value.length > 0) {
    filters["model"] = {
      _icontains: model.value,
    };
  }
  // Always exclude blacklisted models
  if (blacklistedModels.value.length > 0) {
    if (!filters["model"]) filters["model"] = {};
    filters["model"]["_nin"] = blacklistedModels.value;
  }

  // If region is not empty, add it to filters
  if (region.value !== "all") {
    filters["region"] = {
      _icontains: region.value,
    };
  }

  // Handle year range filters (AND condition when both exist)
  if (debouncedYearFrom.value && debouncedYearTo.value) {
    // if (debouncedYearFrom.value > debouncedYearTo.value) {
    //   yearRangeError.value = "年式の下限が上限を上回っています";
    //   return {};
    // } else {
    //   yearRangeError.value = "";
    // }

    filters["model_year"] = {};

    if (debouncedYearFrom.value) {
      filters["model_year"]["_gte"] = `${debouncedYearFrom.value}-01-01`;
    }

    if (debouncedYearTo.value) {
      filters["model_year"]["_lte"] = `${debouncedYearTo.value}-12-31`;
    }
  } else {
    yearRangeError.value = "";
  }

  // Handle price range filters (AND condition when both exist)
  if (debouncedPriceFrom.value || debouncedPriceTo.value) {
    filters["price"] = {};

    if (debouncedPriceFrom.value) {
      filters["price"]["_gte"] = debouncedPriceFrom.value;
    }

    if (debouncedPriceTo.value) {
      filters["price"]["_lte"] = debouncedPriceTo.value;
    }
  }

  // PRICE OVERRIDE based on priceMode
  if (priceMode.value === "priced") {
    // Show only cars with actual prices (not negotiable)
    if (!filters["price"]) {
      filters["price"] = {};
    }
    filters["price"]["_gt"] = 0;
  } else if (priceMode.value === "negotiable") {
    // Show only cars with negotiable prices (0 or null)
    filters["price"] = {
      _or: [{ _eq: 0 }, { _null: true }],
    };
  }
  // if 'all', do nothing - show both priced and negotiable cars

  // Handle mileage range filters (AND condition when both exist)
  if (debouncedMileageFrom.value || debouncedMileageTo.value) {
    filters["mileage"] = {};

    if (debouncedMileageFrom.value) {
      filters["mileage"]["_gte"] = debouncedMileageFrom.value;
    }

    if (debouncedMileageTo.value) {
      filters["mileage"]["_lte"] = debouncedMileageTo.value;
    }
  }

  // If mission is not empty, add it to filters
  if (mission.value) {
    filters["mission"] = {
      _icontains: mission.value,
    };
  }

  // If drivetrain is not empty, add it to filters. (2WD or 4WD)
  if (drivetrain.value) {
    filters["driving_system"] = {
      _eq: drivetrain.value,
    };
  }

  // If engine_type is not empty, add it to filters. (ガソリン, ディーゼル, ハイブリッド, etc.)
  if (engine_type.value) {
    filters["engine_type"] = {
      _icontains: engine_type.value,
    };
  }

  // If accident_history is not empty, add it to filters. (なし, あり)
  if (accident_history.value) {
    filters["fix_history"] = {
      _icontains: accident_history.value,
    };
  }

  // If steering is not empty, add it to filters. (右舵, 左舵)
  if (steering.value) {
    filters["handle"] = {
      _icontains: steering.value,
    };
  }

  return filters;
});

const computedSort = computed(() => {
  if (sort.value === "price_asc") {
    return "price";
  } else if (sort.value === "price_desc") {
    return "-price";
  } else if (sort.value === "model_newest") {
    return "-model_year";
  } else if (sort.value === "model_oldest") {
    return "model_year";
  } else if (sort.value === "scraped_desc") {
    return "-scraped_at";
  } else if (sort.value === "scraped_asc") {
    return "scraped_at";
  } else {
    return "scraped_at";
  }
});

// Get active blacklist keywords
const { data: blacklistData } = useDirectusFetch<Blacklist[]>(
  "/items/blacklist",
  {
    key: "active-blacklist",
    params: {
      filter: { is_active: { _eq: true } },
      fields: ["value"],
    },
  }
);

const blacklistKeywords = computed(
  () => blacklistData.value?.map((item) => item.value.toLowerCase()) ?? []
);

// Filter out blacklisted cars on the frontend
const filteredMinimalCarMetrics = computed(() => {
  if (!minimalCarMetrics.value) return [];
  return minimalCarMetrics.value.filter((car) => {
    const carName = car.car_name?.toLowerCase() || "";
    return !blacklistKeywords.value.some((keyword) =>
      carName.includes(keyword)
    );
  });
});

const { data: minimalCarMetrics, status: minimalStatus } = useDirectusFetch<
  UsedCars[]
>("/items/used_cars", {
  key: "cars-advanced-search-minimal",
  params: {
    filter: computedFilters,
    limit: -1,
    sort: computedSort,
    fields: ["price", "car_name"],
  },
});

const { data: rawCars, status } = useDirectusFetch<UsedCars[]>(
  "/items/used_cars",
  {
    key: "cars-advanced-search",
    params: {
      filter: computedFilters,
      limit: searchLimit,
      sort: computedSort,
      page: currentPage,
    },
  }
);

// Filter out blacklisted cars from the main results
const cars = computed(() => {
  if (!rawCars.value) return [];
  return rawCars.value.filter((car) => {
    const carName = car.car_name?.toLowerCase() || "";
    return !blacklistKeywords.value.some((keyword) =>
      carName.includes(keyword)
    );
  });
});

const totalCars = computed(() => filteredMinimalCarMetrics.value?.length ?? 0);

const handlePageChange = (page: number) => {
  currentPage.value = page;

  // Scroll back up to search results header
  nextTick(() => {
    const element = document.getElementById("search-result-header");
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  });
};

const priceInputModeItems = [
  { label: "スライドバー", value: "slider" },
  { label: "入力フォーム", value: "input" },
];

// Reset to first page when filters change
watch(
  computedFilters,
  () => {
    currentPage.value = 1;

    // Set query params
    const query = {
      maker: maker.value,
      model: model.value,
      yearFrom: yearFrom.value,
      yearTo: yearTo.value,
      priceMode: priceMode.value,
      priceInputMode: priceInputMode.value,
      priceFrom: priceFrom.value,
      priceTo: priceTo.value,
      mileageFrom: mileageFrom.value,
      mileageTo: mileageTo.value,
      mission: mission.value,
      drivetrain: drivetrain.value,
      accident_history: accident_history.value,
      steering: steering.value,
      engine_type: engine_type.value,
      sort: sort.value,
      car_name: car_name.value,
      page: 1,
    };

    // Update URL with filter parameters
    navigateTo(
      {
        path: route.path,
        query: query,
      },
      { replace: true }
    );
  },
  { deep: true }
);

// Reset to first page when sort changes
watch(computedSort, () => {
  currentPage.value = 1;
});

// Reset to first page when items per page changes
watch(searchLimit, () => {
  currentPage.value = 1;
});

const resetFilters = () => {
  car_name.value = "";
  maker.value = "";
  model.value = "";
  region.value = "all";
  yearFrom.value = 1886;
  yearTo.value = 2005;
  priceMode.value = "all";
  priceInputMode.value = "slider";
  priceFrom.value = 0;
  priceTo.value = 10000;
  mileageFrom.value = 0;
  mileageTo.value = 1000000;
  mission.value = "";
  drivetrain.value = "";
  accident_history.value = "";
  steering.value = "";
  engine_type.value = "";
  sort.value = "scraped_desc";
  currentPage.value = 1;
};

const totalCarsWithPrice = computed(
  () =>
    filteredMinimalCarMetrics.value?.filter((car) => car.price && car.price > 0)
      .length ?? 0
);
const totalCarsWithoutPrice = computed(
  () =>
    filteredMinimalCarMetrics.value?.filter(
      (car) => !car.price || car.price == 0 || car.price == ("0.00" as any)
    ).length ?? []
);
const listViewModeItems = [
  { label: "すべて", value: "all" },
  { label: "価格あり", value: "priced" },
  { label: "応談", value: "negotiable" },
];
</script>

<template>
  <UDashboardPanel id="cars">
    <template #header>
      <UDashboardNavbar>
        <template #leading>
          <UButton
            to="/"
            variant="outline"
            color="neutral"
            icon="i-heroicons-chevron-left"
            :external="true"
          >
            戻る
          </UButton>
        </template>
        <template #right>
          <UButton to="/cars/add" variant="outline" icon="i-heroicons-plus">
            <!-- add cars button -->
            新規車両登録
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="-m-3"> 
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-2xl font-bold">
            {{ route.query.maker ? route.query.maker : "すべての中古車" }}
            {{ route.query.model ? route.query.model : "" }}
          </h2>
          <UButton
            @click="resetFilters"
            variant="ghost"
            color="neutral"
            icon="i-heroicons-x-mark"
            size="sm"
          >
            フィルターをリセット
          </UButton>
        </div>
        <!-- Advance search filtering -->
        <div
          id="advance-filter"
          class="m-0 p-4 lg:p-8 grid grid-cols-3 lg:grid-cols-6 gap-3 md:gap-6 xl:grid-cols-8 rounded bg-gray-100"
        >
          <div class="flex items-center col-span-3 md:col-span-2">
            <span class="mr-2 min-w-[80px] text-xs font-medium text-right">車名</span>
            <UInput
              v-model="car_name"
              label="車名"
              placeholder="車名を入力"
              class="grow-1"
            />
          </div>

          <div class="flex items-center col-span-3 md:col-span-2">
            <span class="mr-2 min-w-[80px] text-xs font-medium text-right">メーカー</span>
            <UInput
              v-model="maker"
              label="メーカー"
              placeholder="メーカーを入力"
              class="grow-1"
            />
          </div>

          <div class="flex items-center col-span-3 md:col-span-2">
            <span class="mr-2 min-w-[80px] text-xs font-medium text-right">モデル</span>
            <UInput
              v-model="model"
              label="モデル"
              placeholder="モデルを入力"
              class="grow-1"
            />
          </div>

          <div class="flex items-center col-span-3 md:col-span-2">
            <span class="mr-2 min-w-[80px] text-xs font-medium text-right">地域</span>
            <USelect
              v-model="region"
              label="地域"
              placeholder="すべて"
              class="h-8 w-full"
              :items="[
                { label: 'すべて', value: 'all' },
                { label: '日本', value: 'カーセンサー' },
                { label: '欧州', value: 'mobile.de' },
              ]"
            />
          </div>


          <div
            class="flex flex-col col-span-3 md:col-span-2 justify-center"
            v-auto-animate
          >
            <div class="flex items-center">
              <span class="mr-2 min-w-[80px] text-xs font-medium text-right">年式</span>
              <UInputNumber
                orientation="vertical"
                :format-options="{
                  useGrouping: false,
                }"
                v-model="yearFrom"
                label="年式"
                placeholder="年式を入力"
                class="grow-1"
                :color="yearRangeError ? 'error' : undefined"
                :max="yearTo"
              />
              <span class="mx-2">~</span>
              <UInputNumber
                orientation="vertical"
                :format-options="{
                  useGrouping: false,
                }"
                v-model="yearTo"
                label="年式"
                placeholder="年式を入力"
                class="grow-1"
                :color="yearRangeError ? 'error' : undefined"
                :min="yearFrom"
              />
            </div>
            <UAlert
              v-if="yearRangeError"
              color="error"
              variant="soft"
              :description="yearRangeError"
              class="mt-2"
              :ui="{ description: 'text-xs' }"
            />
          </div>

          <div class="flex items-center col-span-3 md:col-span-2">

            <span class="mr-2 min-w-[80px] text-xs font-medium text-right">走行距離</span>
            <UInputNumber
              orientation="vertical"
              v-model="mileageFrom"
              label="走行距離"
              placeholder="走行距離を入力"
              class="grow-1"
              :format-options="{
                useGrouping: false,
              }"
              :max="mileageTo"
            />
            <span class="mx-2">~</span>
            <UInputNumber
              orientation="vertical"
              v-model="mileageTo"
              label="走行距離"
              placeholder="走行距離を入力"
              class="grow-1"
              :format-options="{
                useGrouping: false,
              }"
              :min="mileageFrom"
            />
          </div>

          <div
            class="flex flex-col col-span-3 md:col-span-2 lg:col-span-3 xl:col-span-2"
          >
                         <div class="flex items-center mb-1">
               <span class="mr-2 min-w-[80px] text-xs font-medium text-right">価格</span>
              <!-- Switch between Slider or Inputs -->
              <URadioGroup
                v-model="priceInputMode"
                :items="priceInputModeItems"
                orientation="horizontal"
                :ui="{
                  fieldset: 'flex-wrap',
                  label: '-ml-1 min-w-max cursor-pointer',
                }"
              />
            </div>
            <div class="flex items-center" v-if="priceInputMode === 'input'">
              <UInputNumber
                orientation="vertical"
                :format-options="{
                  useGrouping: false,
                }"
                :max="priceTo"
                v-model="priceFrom"
                label="価格"
                placeholder="価格を入力"
                class="grow-1"
              /><span class="ml-1 min-w-max text-xs">万円</span>
              <span class="mx-2">~</span>
              <UInputNumber
                orientation="vertical"
                :format-options="{
                  useGrouping: false,
                }"
                :min="priceFrom"
                v-model="priceTo"
                label="価格"
                placeholder="価格を入力"
                class="grow-1"
              /><span class="ml-1 min-w-max text-xs">万円</span>
            </div>
            <div
              v-if="priceInputMode === 'slider'"
              class="flex gap-2 items-center text-xs"
            >
              <p class="text-xs break-keep">
                {{ sliderPrice[0] === 0 ? "下限なし" : sliderPrice[0] }}
              </p>
              <USlider
                v-model="sliderPrice"
                :min="0"
                :max="10000"
                class="h-8"
              />
              <p class="text-xs break-keep">
                {{ sliderPrice[1] === 10000 ? "上限なし" : sliderPrice[1] }}
              </p>
            </div>
          </div>

                     <div class="flex items-center col-span-3 sm:col-span-3 lg:col-span-2">
             <span class="mr-2 min-w-[80px] text-xs font-medium text-right">ミッション</span>
            <URadioGroup
              v-model="mission"
              :items="transmissionItems"
              orientation="horizontal"
              :ui="{
                fieldset: 'flex-wrap',
                label: '-ml-1 min-w-max cursor-pointer',
                item: 'cursor-pointer',
              }"
            />
          </div>

                     <div class="flex items-center col-span-3 sm:col-span-3 lg:col-span-2">
             <span class="mr-2 min-w-[80px] text-xs font-medium text-right">駆動方式</span>
            <URadioGroup
              v-model="drivetrain"
              :items="drivetrainItems"
              orientation="horizontal"
              :ui="{
                fieldset: 'flex-wrap',
                label: '-ml-1 min-w-max cursor-pointer',
              }"
            />
          </div>

                     <div class="flex items-center col-span-3 md:col-span-2">
             <span class="mr-2 min-w-[80px] text-xs font-medium text-right">修復歴</span>
            <URadioGroup
              v-model="accident_history"
              :items="accidentHistoryItems"
              orientation="horizontal"
              :ui="{
                fieldset: 'flex-wrap',
                label: '-ml-1 min-w-max cursor-pointer',
              }"
            />
          </div>

                     <div class="flex items-center col-span-3 md:col-span-2">
             <span class="mr-2 min-w-[80px] text-xs font-medium text-right">ハンドル</span>
            <URadioGroup
              v-model="steering"
              :items="steeringItems"
              orientation="horizontal"
              :ui="{
                fieldset: 'flex-wrap',
                label: '-ml-1 min-w-max cursor-pointer',
              }"
            />
          </div>

                     <div class="flex items-center col-span-3 md:col-span-4">
             <span class="mr-2 min-w-[80px] text-xs font-medium text-right">エンジン種類</span>
            <URadioGroup
              v-model="engine_type"
              :items="engineTypeItems"
              orientation="horizontal"
              :ui="{
                fieldset: 'flex-wrap',
                label: '-ml-1 min-w-max cursor-pointer',
              }"
            />
          </div>
        </div>

        <!-- Price Range Charts with loading overlay -->
        <div class="relative md:p-8 mx-auto mt-4">
          <PriceAggregationChart
            :cars="
              minimalStatus === 'success' ? filteredMinimalCarMetrics ?? [] : []
            "
          />
          <!-- Loading overlay -->
          <div
            v-if="minimalStatus === 'pending'"
            class="absolute inset-0 bg-white/10 backdrop-blur-sm flex items-center justify-center rounded-lg"
          >
            <div class="text-center">
              <div
                class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"
              ></div>
              <p class="text-gray-600">データを読み込んでいます...</p>
            </div>
          </div>
        </div>

        <!-- Car search result list -->
        <div
          id="search-result-header"
          class="flex flex-col md:flex-row gap-2 items-start md:items-end max-w-6xl mx-auto py-2 px-4"
        >
          <div class="items-start text-left w-full">
            <div
              class="text-2xl font-bold flex items-baseline translate-y-1"
              v-auto-animate
            >
              <div
                v-if="minimalStatus === 'success'"
                class="text-primary text-4xl"
              >
                {{ totalCars === 0 ? "該当なし" : totalCars }}
              </div>
              <div v-else class="text-4xl animate-pulse">- - -</div>
              <div class="-translate-y-0.5 text-md">台</div>
            </div>

            <p class="text-muted text-sm">
              (
              <span>価格有: {{ totalCarsWithPrice }}台 |</span>
              <span> 応談: {{ totalCarsWithoutPrice }}台</span>
              )
            </p>
          </div>

          <div
            class="md:ml-auto grid md:flex md:justify-end grid-cols-3 w-full gap-1"
          >
            <!-- View mode -->
            <div class="md:flex gap-1">
              <p class="ml-auto text-sm">価格</p>
              <USelect
                v-model="priceMode"
                :items="listViewModeItems"
                class="w-full md:w-32"
              />
            </div>

            <div class="md:flex gap-1">
              <!-- Sort by -->
              <p class="ml-2 text-sm">並び順</p>
              <USelect
                v-model="sort"
                :items="sortItems"
                class="w-full md:w-32"
              />
            </div>
            <!-- Products per page -->
            <div class="md:flex gap-1">
              <p class="ml-2 text-sm">表示台数</p>
              <USelect
                v-model="searchLimit"
                :items="limitItems"
                class="w-full md:w-32"
              />
            </div>
          </div>
        </div>

        <div
          id="car-list"
          class="grid grid-cols-1 3xl:grid-cols-2 gap-6 mt-2 max-w-6xl mx-auto"
        >
          <!-- Loading -->
          <template v-if="status === 'pending'" class="2xl:col-span-2">
            <SkeletonCarCard />
          </template>

          <LazyHorizontalCarCard :car v-for="car in cars" :key="car.id" />
        </div>

        <!-- Pagination -->
        <div
          class="sticky bottom-4 flex justify-center z-10 mt-8 p-3 py-2 bg-white/80 ring ring-gray-200 backdrop-blur-lg rounded-full w-max mx-auto"
        >
          <UPagination
            v-model:page="currentPage"
            :total="totalCars"
            :items-per-page="searchLimit"
            :sibling-count="2"
            show-edges
            @update:page="handlePageChange"
            :ui="{
              first: 'disabled:opacity-100 disabled:text-dimmed',
              prev: 'disabled:opacity-100 disabled:text-dimmed',
              next: 'disabled:opacity-100 disabled:text-dimmed',
              last: 'disabled:opacity-100 disabled:text-dimmed',
            }"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
