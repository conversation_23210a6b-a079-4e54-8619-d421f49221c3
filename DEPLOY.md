# Quick Deployment Guide

## 🚀 One-Command Production Deployment

For production deployment using the pre-built Docker image:

```bash
# Download the production compose file and start all services
curl -O https://raw.githubusercontent.com/cis-gk-dev/usedcarsales-nuxt/main/docker-compose.prod.yml
docker-compose -f docker-compose.prod.yml up -d
```

Or if you have the repository:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 Quick Setup

1. **Download files** (if not using git):
   ```bash
   curl -O https://raw.githubusercontent.com/cis-gk-dev/usedcarsales-nuxt/main/docker-compose.prod.yml
   curl -O https://raw.githubusercontent.com/cis-gk-dev/usedcarsales-nuxt/main/.env.example
   mv .env.example .env
   mkdir -p scraper/database/init
   curl -o scraper/database/init/01-init.sql https://raw.githubusercontent.com/cis-gk-dev/usedcarsales-nuxt/main/scraper/database/init/01-init.sql
   ```

2. **Configure environment** (edit `.env` as needed):
   ```bash
   # Port configuration - change if you have conflicts on Dokploy/Docker host
   APP_PORT=3001              # Main application port
   DIRECTUS_PORT=8056         # Directus admin interface port  
   DB_EXTERNAL_PORT=26258     # CockroachDB connection port
   DB_UI_PORT=8081            # CockroachDB web UI port
   
   # Security - CHANGE THESE FOR PRODUCTION!
   DIRECTUS_KEY=your-secure-32-character-key
   DIRECTUS_SECRET=your-secure-64-character-secret
   DIRECTUS_ADMIN_EMAIL=<EMAIL>
   DIRECTUS_ADMIN_PASSWORD=your-secure-password
   
   # Cache Configuration (optional)
   DIRECTUS_CACHE_ENABLED=true        # Enable/disable Directus caching
   DIRECTUS_CACHE_STORE=memory        # Cache store: memory, redis, memcache
   DIRECTUS_CACHE_TTL=30m             # Cache TTL (30m, 1h, 1d)
   ```

3. **Start services**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

4. **Access the application**:
   - Main app: http://localhost:3001 (or your configured APP_PORT)
   - Directus admin: http://localhost:8056 (<EMAIL> / admin123)
   - CockroachDB UI: http://localhost:8081

## 🔒 Security Configuration

**IMPORTANT**: For production, edit your `.env` file and change these values:

```bash
# Generate secure keys: openssl rand -hex 32
DIRECTUS_KEY=your-secure-32-character-key
DIRECTUS_SECRET=your-secure-64-character-secret

# Use your admin credentials
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=your-secure-password

# Update public URL if hosting remotely
DIRECTUS_PUBLIC_URL=https://yourdomain.com:8056

# Cache Configuration
DIRECTUS_CACHE_ENABLED=true           # Toggle caching on/off
DIRECTUS_CACHE_STORE=memory           # memory, redis, memcache
DIRECTUS_CACHE_TTL=1h                 # 30m, 1h, 1d, etc.
DIRECTUS_CACHE_AUTO_PURGE=true        # Auto-purge on data changes
```

## 🚀 Cache Options

**Cache Stores**:
- `memory` - In-memory cache (default, fastest, but not persistent)
- `redis` - Redis cache (persistent, scalable, included in docker-compose)
- `memcache` - Memcached cache (requires separate Memcached server)

**Cache TTL Examples**:
- `30s` - 30 seconds
- `5m` - 5 minutes  
- `1h` - 1 hour
- `1d` - 1 day

**Quick Toggle**:
```bash
# Disable caching
DIRECTUS_CACHE_ENABLED=false

# Enable memory cache (default)
DIRECTUS_CACHE_ENABLED=true
DIRECTUS_CACHE_STORE=memory
DIRECTUS_CACHE_TTL=1h

# Enable Redis cache (persistent, recommended for production)
DIRECTUS_CACHE_ENABLED=true
DIRECTUS_CACHE_STORE=redis
DIRECTUS_CACHE_TTL=1h
DIRECTUS_CACHE_REDIS=redis://redis:6379
```

## 🔴 Redis Cache

Redis is included in the docker-compose setup and provides:
- ✅ **Persistent cache** (survives container restarts)
- ✅ **Scalable** (can be shared across multiple Directus instances)
- ✅ **Configurable** (TTL, memory limits, etc.)

**To use Redis cache**:
```bash
DIRECTUS_CACHE_STORE=redis
```

Redis will be automatically available at `redis:6379` within the container network.

## 🛠 Development Mode

For development with hot-reload and local building:

```bash
docker-compose up -d
```

This uses the original `docker-compose.yml` which builds the image locally.

## 🏗 Available Images

The GitHub Actions workflow automatically builds and publishes images for:
- `ghcr.io/cis-gk-dev/usedcarsales-nuxt:latest` (main branch)
- `ghcr.io/cis-gk-dev/usedcarsales-nuxt:dokploy` (dokploy branch)
- `ghcr.io/cis-gk-dev/usedcarsales-nuxt:v1.0.0` (version tags)

## 📊 Service URLs (Default Ports)

| Service | URL | Credentials | ENV Variable |
|---------|-----|-------------|--------------|
| Main App | http://localhost:3001 | - | APP_PORT |
| Directus Admin | http://localhost:8056 | <EMAIL> / admin123 | DIRECTUS_PORT |
| CockroachDB UI | http://localhost:8081 | - | DB_UI_PORT |
| Database | localhost:26258 | root / (no password) | DB_EXTERNAL_PORT |

## 🔍 Health Checks

- App health: http://localhost:3001/api/database/health (or your configured APP_PORT)
- Database stats: http://localhost:3001/api/database/stats
- Start scraper: `curl -X POST http://localhost:3001/api/scraper/start -H "Content-Type: application/json" -d '{"outputMode":"database"}'`

## 🐳 Docker Commands

```bash
# Start all services
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop all services
docker-compose -f docker-compose.prod.yml down

# Update to latest image
docker-compose -f docker-compose.prod.yml pull nuxt-app
docker-compose -f docker-compose.prod.yml up -d nuxt-app
```