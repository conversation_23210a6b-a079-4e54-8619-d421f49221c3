import { createRequire } from 'module';

// ES module equivalent of require for CommonJS modules
const require = createRequire(import.meta.url);

// Load environment variables
if (!process.env.NUXT_ENV_INIT) {
  try {
    const dotenv = await import('dotenv');
    dotenv.config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

// Import CommonJS modules using require (since they haven't been converted to ES modules yet)
const dbConnection = require('../../scraper/database/connection');
const batchUpsert = require('../../scraper/database/batch-upsert');

// Sample data pools based on real CarSensor data
const SAMPLE_DATA = {
  makers: [
    'トヨタ', 'ホンダ', '日産', 'マツダ', 'スバル', 'スズキ', 'ダイハツ', 'いすゞ', '三菱',
    'メルセデス・ベンツ', 'BMW', 'アウディ', 'フォルクスワーゲン', 'ポルシェ', 'ボルボ',
    'フォード', 'シボレー', 'キャデラック', 'ジープ', 'クライスラー', 'ダッジ',
    'フェラーリ', 'ランボルギーニ', 'マセラティ', 'アルファロメオ', 'フィアット',
    'ジャガー', 'ランドローバー', 'ロールスロイス', 'ベントレー', 'アストンマーティン'
  ],

  models: {
    'トヨタ': ['プリウス', 'カムリ', 'カローラ', 'ヴィッツ', 'アクア', 'ハリアー', 'ランドクルーザー', 'アルファード', 'ヴェルファイア', 'クラウン'],
    'ホンダ': ['フィット', 'ヴェゼル', 'フリード', 'ステップワゴン', 'オデッセイ', 'アコード', 'シビック', 'CR-V', 'N-BOX', 'N-WGN'],
    '日産': ['ノート', 'セレナ', 'エクストレイル', 'スカイライン', 'フーガ', 'リーフ', 'ジューク', 'キューブ', 'マーチ', 'ティーダ'],
    'マツダ': ['デミオ', 'アクセラ', 'アテンザ', 'CX-3', 'CX-5', 'CX-8', 'ロードスター'],
    'スバル': ['インプレッサ', 'レガシィ', 'フォレスター', 'XV', 'アウトバック', 'WRX'],
    'スズキ': ['ワゴンR', 'スイフト', 'ハスラー', 'ジムニー', 'アルト', 'スペーシア'],
    'メルセデス・ベンツ': ['Aクラス', 'Cクラス', 'Eクラス', 'Sクラス', 'GLA', 'GLC', 'GLE', 'GLS', 'AMG GT', 'Gクラス'],
    'BMW': ['1シリーズ', '3シリーズ', '5シリーズ', '7シリーズ', 'X1', 'X3', 'X5', 'X7', 'Z4', 'i3'],
    'アウディ': ['A1', 'A3', 'A4', 'A6', 'A8', 'Q2', 'Q3', 'Q5', 'Q7', 'TT'],
    'フォルクスワーゲン': ['ポロ', 'ゴルフ', 'パサート', 'ティグアン', 'トゥアレグ', 'アップ!'],
    'ポルシェ': ['911', 'ボクスター', 'ケイマン', 'マカン', 'カイエン', 'パナメーラ', 'タイカン'],
    'フォード': ['フィエスタ', 'フォーカス', 'マスタング', 'エクスプローラー', 'F-150'],
    'シボレー': ['カマロ', 'コルベット', 'クルーズ', 'タホ', 'シルバラード'],
    'フェラーリ': ['488', 'F8', 'ポルトフィーノ', 'ローマ', 'SF90'],
    'ランボルギーニ': ['ウラカン', 'アヴェンタドール', 'ウルス'],
    'マセラティ': ['ギブリ', 'クアトロポルテ', 'レヴァンテ', 'グラントゥーリズモ'],
    'ジャガー': ['XE', 'XF', 'XJ', 'F-PACE', 'E-PACE', 'I-PACE'],
    'ランドローバー': ['ディスカバリー', 'レンジローバー', 'ディフェンダー', 'イヴォーク']
  },

  // Fallback models for makers without specific models
  fallbackModels: ['セダン', 'ハッチバック', 'SUV', 'クーペ', 'ワゴン', 'コンバーチブル', 'ミニバン', 'コンパクト', 'スポーツ', 'ラグジュアリー'],

  engineTypes: ['ガソリン', 'ディーゼル', 'ハイブリッド', '電気', 'プラグインハイブリッド'],
  drivingSystems: ['2WD', '4WD', 'AWD', 'FF', 'FR', 'MR', 'RR'],
  missionTypes: ['MT', 'AT/CVT'],
  handles: ['右', '左'],
  fixHistories: ['なし', 'あり', '修復歴なし', '軽微な修復歴あり'],
  regions: ['東京都', '大阪府', '愛知県', '神奈川県', '埼玉県', '千葉県', '兵庫県', '福岡県', '北海道', '宮城県'],

  safeEquipment: [
    'パワステ', 'ABS', 'エアバッグ：運転席/助手席', 'エアバッグ：サイド', 'エアバッグ：カーテン',
    '横滑り防止装置', '盗難防止装置', 'クルーズコントロール', '衝突被害軽減ブレーキ', 'レーンキープアシスト'
  ],

  comfortEquipment: [
    'エアコン・クーラー', 'カーナビ：メモリー他', 'TV：ワンセグ', 'TV：フルセグ', '映像：DVD',
    'オーディオ：CD', 'ミュージックプレイヤー接続可', 'ETC', 'ドライブレコーダー', 'バックカメラ'
  ],

  interiorEquipment: [
    'キーレス', 'パワーウインドウ', '本革シート', 'シートヒーター', 'パワーシート',
    'ウォークスルー', 'フルフラットシート', '3列シート', 'ベンチシート', 'スマートキー'
  ],

  exteriorEquipment: [
    'フロントフォグランプ', 'アルミホイール', 'サンルーフ', 'ルーフレール', 'エアロ',
    'ローダウン', 'リフトアップ', 'ワイドボディ', 'HIDライト', 'LEDライト'
  ]
};

// Utility functions
function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function randomChoices(array, count = null) {
  if (count === null) {
    count = Math.floor(Math.random() * Math.min(array.length, 5)) + 1;
  }
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}



function generateModelYear() {
  const year = randomInt(1990, 2024);
  return `${year}/1/1`;
}

function generatePrice() {
  const priceRanges = [
    { min: 10, max: 99, range: '0~100万円' },
    { min: 100, max: 199, range: '101~200万円' },
    { min: 200, max: 299, range: '201~300万円' },
    { min: 300, max: 399, range: '301~400万円' },
    { min: 400, max: 499, range: '401~500万円' },
    { min: 500, max: 599, range: '501~600万円' },
    { min: 600, max: 799, range: '601~800万円' },
    { min: 800, max: 999, range: '801~1000万円' },
    { min: 1000, max: 2000, range: '1001万円~' }
  ];

  const priceRange = randomChoice(priceRanges);
  const price = randomInt(priceRange.min, priceRange.max);

  return {
    price: price.toString(),
    price_range: priceRange.range
  };
}

function generateMileage() {
  // Generate realistic mileage (in km)
  const baseMileage = randomInt(1000, 200000);
  return Math.round(baseMileage / 1000) * 1000; // Round to nearest 1000
}

function generateDisplacement() {
  const displacements = [660, 1000, 1300, 1500, 1600, 1800, 2000, 2400, 2500, 3000, 3500, 4000, 5000, 6000];
  return randomChoice(displacements);
}

function generateCarName(_maker, model) {
  const grades = ['', 'S', 'X', 'G', 'L', 'DX', 'LX', 'EX', 'ハイブリッド', 'ターボ', '4WD'];
  const grade = Math.random() > 0.5 ? ` ${randomChoice(grades)}` : '';
  const options = Math.random() > 0.7 ? ` ${randomChoice(['ナビ付', 'サンルーフ', '本革', 'エアロ', '車検付'])}` : '';

  return `${model}${grade}${options}`.trim();
}

function generateDetail() {
  const details = [
    '車検整備付き、記録簿完備',
    '禁煙車、ワンオーナー車',
    '修復歴なし、点検整備済み',
    'ディーラー車、保証付き',
    '低走行車、美車',
    '社外ナビ、ETC付き',
    'バックカメラ、ドライブレコーダー付き',
    '本革シート、シートヒーター付き',
    'サンルーフ、アルミホイール',
    '車検令和7年まで'
  ];

  return Math.random() > 0.3 ? randomChoice(details) : '';
}

function generateSourceURL(index) {
  return `https://www.carsensor.net/usedcar/detail/SAMPLE${String(index).padStart(10, '0')}/index.html`;
}

function generateImageURL() {
  return 'https://placehold.co/600x400.jpg';
}

function generateVehicleInspection() {
  const year = randomInt(2024, 2027);
  const month = randomInt(1, 12);
  return `${year}(R${year - 2018})年${String(month).padStart(2, '0')}月`;
}

function generateInsurance() {
  return randomChoice(['保証付', '保証無', '保証3ヶ月', '保証6ヶ月', '保証1年']);
}

function generateMaintenance() {
  return randomChoice(['法定整備付', '法定整備無', '整備込', '整備別途', '点検整備済']);
}

function generateMission() {
  const missions = [
    'フロア5AT', 'フロア6AT', 'フロアCVT', 'フロア5MT', 'フロア6MT',
    'コラム4AT', 'フロアMTモード付CVT', 'フロアMTモード付5AT', 'フロアMTモード付6AT'
  ];
  return randomChoice(missions);
}

// Generate a single car record
function generateCarRecord(index) {
  const maker = randomChoice(SAMPLE_DATA.makers);
  const models = SAMPLE_DATA.models[maker] || SAMPLE_DATA.fallbackModels;
  const model = randomChoice(models);
  const carName = generateCarName(maker, model);
  const pricing = generatePrice();

  return {
    car_name: carName,
    detail: generateDetail(),
    displacement: generateDisplacement(),
    driving_system: randomChoice(SAMPLE_DATA.drivingSystems),
    engine_type: randomChoice(SAMPLE_DATA.engineTypes),
    fix_history: randomChoice(SAMPLE_DATA.fixHistories),
    handle: randomChoice(SAMPLE_DATA.handles),
    image: generateImageURL(),
    insurance: generateInsurance(),
    maintenance: generateMaintenance(),
    maker: maker,
    mileage: generateMileage(),
    mission: generateMission(),
    Mission_type: randomChoice(SAMPLE_DATA.missionTypes),
    model: model,
    model_year: generateModelYear(),
    price: pricing.price,
    price_range: pricing.price_range,
    region: randomChoice(SAMPLE_DATA.regions),
    source: 'カーセンサー',
    sourceURL: generateSourceURL(index),
    vehicle_inspection: generateVehicleInspection(),
    safe_equipment: randomChoices(SAMPLE_DATA.safeEquipment).join('\n'),
    comfort_equipment: randomChoices(SAMPLE_DATA.comfortEquipment).join('\n'),
    interia_equipment: randomChoices(SAMPLE_DATA.interiorEquipment).join('\n'),
    exteria_equipment: randomChoices(SAMPLE_DATA.exteriorEquipment).join('\n')
  };
}

// Main generation function
async function generateSampleData(totalRecords = 1000000, batchSize = 1000) {
  console.log(`🚀 Starting generation of ${totalRecords.toLocaleString()} sample car records...`);
  console.log(`📦 Batch size: ${batchSize.toLocaleString()}`);

  try {
    // Connect to database
    await dbConnection.connect();
    console.log('✅ Database connected');

    let totalProcessed = 0;
    const startTime = Date.now();

    for (let i = 0; i < totalRecords; i += batchSize) {
      const currentBatchSize = Math.min(batchSize, totalRecords - i);
      const batch = [];

      // Generate batch
      for (let j = 0; j < currentBatchSize; j++) {
        batch.push(generateCarRecord(i + j + 1));
      }

      // Insert batch to database
      try {
        const result = await batchUpsert.upsertCars(batch);
        totalProcessed += currentBatchSize;

        const progress = ((totalProcessed / totalRecords) * 100).toFixed(1);
        const elapsed = (Date.now() - startTime) / 1000;
        const rate = totalProcessed / elapsed;
        const eta = (totalRecords - totalProcessed) / rate;

        console.log(`📊 Progress: ${progress}% (${totalProcessed.toLocaleString()}/${totalRecords.toLocaleString()}) | Rate: ${Math.round(rate)}/sec | ETA: ${Math.round(eta)}s`);

        if (result.errors.length > 0) {
          console.warn(`⚠️  ${result.errors.length} errors in batch ${Math.floor(i / batchSize) + 1}`);
        }

      } catch (error) {
        console.error(`❌ Failed to insert batch ${Math.floor(i / batchSize) + 1}:`, error.message);
        // Continue with next batch instead of failing completely
      }
    }

    const totalTime = (Date.now() - startTime) / 1000;
    console.log(`\n🎉 Generation completed!`);
    console.log(`📈 Total records processed: ${totalProcessed.toLocaleString()}`);
    console.log(`⏱️  Total time: ${Math.round(totalTime)}s`);
    console.log(`📊 Average rate: ${Math.round(totalProcessed / totalTime)}/sec`);

  } catch (error) {
    console.error('❌ Generation failed:', error);
    process.exit(1);
  } finally {
    await dbConnection.close();
  }
}

// CLI interface - ES module equivalent of require.main === module
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const totalRecords = args[0] ? parseInt(args[0]) : 1000000;
  const batchSize = args[1] ? parseInt(args[1]) : 1000;

  if (isNaN(totalRecords) || totalRecords <= 0) {
    console.error('❌ Invalid total records count');
    process.exit(1);
  }

  if (isNaN(batchSize) || batchSize <= 0) {
    console.error('❌ Invalid batch size');
    process.exit(1);
  }

  generateSampleData(totalRecords, batchSize).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { generateSampleData, generateCarRecord };
