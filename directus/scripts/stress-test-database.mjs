import { createRequire } from 'module';
import { performance } from 'perf_hooks';

// ES module equivalent of require for CommonJS modules
const require = createRequire(import.meta.url);

// Load environment variables
if (!process.env.NUXT_ENV_INIT) {
  try {
    const dotenv = await import('dotenv');
    dotenv.config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

// Import CommonJS modules
const dbConnection = require('../../scraper/database/connection');

class DatabaseStressTest {
  constructor() {
    this.results = [];
    this.concurrentQueries = parseInt(process.env.CONCURRENT_QUERIES) || 5;
    this.iterations = parseInt(process.env.TEST_ITERATIONS) || 10;
  }

  /**
   * Execute a query and measure performance
   */
  async executeQuery(name, query, params = []) {
    const startTime = performance.now();
    try {
      const result = await dbConnection.query(query, params);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        name,
        success: true,
        duration: Math.round(duration * 100) / 100, // Round to 2 decimal places
        rowCount: result.rows.length,
        query: query.replace(/\s+/g, ' ').trim()
      };
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        name,
        success: false,
        duration: Math.round(duration * 100) / 100,
        error: error.message,
        query: query.replace(/\s+/g, ' ').trim()
      };
    }
  }

  /**
   * Test basic SELECT queries
   */
  async testBasicQueries() {
    console.log('🔍 Testing basic SELECT queries...');
    
    const queries = [
      {
        name: 'Count all records',
        query: 'SELECT COUNT(*) as total FROM used_cars'
      },
      {
        name: 'Select first 100 records',
        query: 'SELECT * FROM used_cars LIMIT 100'
      },
      {
        name: 'Select by maker (Toyota)',
        query: 'SELECT * FROM used_cars WHERE maker = $1 LIMIT 50',
        params: ['トヨタ']
      },
      {
        name: 'Select by price range',
        query: 'SELECT * FROM used_cars WHERE price BETWEEN $1 AND $2 LIMIT 50',
        params: [100, 500]
      },
      {
        name: 'Select by mileage',
        query: 'SELECT * FROM used_cars WHERE mileage < $1 LIMIT 50',
        params: [50000]
      }
    ];

    for (const { name, query, params } of queries) {
      const result = await this.executeQuery(name, query, params);
      this.results.push(result);
      console.log(`  ${result.success ? '✅' : '❌'} ${name}: ${result.duration}ms (${result.rowCount || 0} rows)`);
    }
  }

  /**
   * Test complex queries with JOINs and aggregations
   */
  async testComplexQueries() {
    console.log('🧮 Testing complex queries...');
    
    const queries = [
      {
        name: 'Group by maker with count',
        query: `
          SELECT maker, COUNT(*) as count, AVG(price) as avg_price 
          FROM used_cars 
          WHERE maker IS NOT NULL 
          GROUP BY maker 
          ORDER BY count DESC 
          LIMIT 20
        `
      },
      {
        name: 'Price statistics by region',
        query: `
          SELECT region, 
                 COUNT(*) as count,
                 MIN(price) as min_price,
                 MAX(price) as max_price,
                 AVG(price) as avg_price
          FROM used_cars 
          WHERE region IS NOT NULL AND price IS NOT NULL
          GROUP BY region 
          ORDER BY avg_price DESC
        `
      },
      {
        name: 'Mileage distribution',
        query: `
          SELECT 
            CASE 
              WHEN mileage < 10000 THEN '0-10k'
              WHEN mileage < 50000 THEN '10k-50k'
              WHEN mileage < 100000 THEN '50k-100k'
              ELSE '100k+'
            END as mileage_range,
            COUNT(*) as count,
            AVG(price) as avg_price
          FROM used_cars 
          WHERE mileage IS NOT NULL
          GROUP BY mileage_range
          ORDER BY avg_price DESC
        `
      },
      {
        name: 'Equipment search (JSONB)',
        query: `
          SELECT car_name, maker, model, price, safe_equipment
          FROM used_cars 
          WHERE safe_equipment ? 'ABS'
          LIMIT 100
        `
      }
    ];

    for (const { name, query, params } of queries) {
      const result = await this.executeQuery(name, query, params);
      this.results.push(result);
      console.log(`  ${result.success ? '✅' : '❌'} ${name}: ${result.duration}ms (${result.rowCount || 0} rows)`);
    }
  }

  /**
   * Test full-text search queries
   */
  async testSearchQueries() {
    console.log('🔎 Testing search queries...');
    
    const queries = [
      {
        name: 'Car name search (LIKE)',
        query: `
          SELECT * FROM used_cars 
          WHERE car_name ILIKE $1 
          LIMIT 50
        `,
        params: ['%プリウス%']
      },
      {
        name: 'Multiple field search',
        query: `
          SELECT * FROM used_cars 
          WHERE (car_name ILIKE $1 OR model ILIKE $1) 
          AND price BETWEEN $2 AND $3
          LIMIT 50
        `,
        params: ['%ハイブリッド%', 200, 800]
      },
      {
        name: 'Equipment contains search',
        query: `
          SELECT * FROM used_cars 
          WHERE safe_equipment::text ILIKE $1
          LIMIT 50
        `,
        params: ['%エアバッグ%']
      }
    ];

    for (const { name, query, params } of queries) {
      const result = await this.executeQuery(name, query, params);
      this.results.push(result);
      console.log(`  ${result.success ? '✅' : '❌'} ${name}: ${result.duration}ms (${result.rowCount || 0} rows)`);
    }
  }

  /**
   * Test concurrent queries
   */
  async testConcurrentQueries() {
    console.log(`⚡ Testing concurrent queries (${this.concurrentQueries} concurrent)...`);
    
    const baseQuery = 'SELECT * FROM used_cars WHERE maker = $1 LIMIT 100';
    const makers = ['トヨタ', 'ホンダ', '日産', 'BMW', 'メルセデス・ベンツ'];
    
    const startTime = performance.now();
    
    const promises = [];
    for (let i = 0; i < this.concurrentQueries; i++) {
      const maker = makers[i % makers.length];
      promises.push(this.executeQuery(`Concurrent query ${i + 1}`, baseQuery, [maker]));
    }
    
    const results = await Promise.all(promises);
    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    
    this.results.push(...results);
    
    const successCount = results.filter(r => r.success).length;
    console.log(`  ✅ ${successCount}/${this.concurrentQueries} queries succeeded`);
    console.log(`  ⏱️  Total time: ${Math.round(totalDuration)}ms`);
    console.log(`  📊 Average per query: ${Math.round(totalDuration / this.concurrentQueries)}ms`);
  }

  /**
   * Test repeated queries for consistency
   */
  async testRepeatedQueries() {
    console.log(`🔄 Testing repeated queries (${this.iterations} iterations)...`);
    
    const query = 'SELECT COUNT(*) FROM used_cars WHERE price BETWEEN $1 AND $2';
    const params = [300, 600];
    
    const durations = [];
    
    for (let i = 0; i < this.iterations; i++) {
      const result = await this.executeQuery(`Iteration ${i + 1}`, query, params);
      durations.push(result.duration);
      
      if (i === 0) {
        console.log(`  📊 First result: ${result.rowCount} rows`);
      }
    }
    
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    
    console.log(`  ⏱️  Average: ${Math.round(avgDuration * 100) / 100}ms`);
    console.log(`  🚀 Fastest: ${minDuration}ms`);
    console.log(`  🐌 Slowest: ${maxDuration}ms`);
  }

  /**
   * Generate performance report
   */
  generateReport() {
    console.log('\n📊 PERFORMANCE REPORT');
    console.log('=' .repeat(50));
    
    const successfulQueries = this.results.filter(r => r.success);
    const failedQueries = this.results.filter(r => !r.success);
    
    console.log(`📈 Total queries executed: ${this.results.length}`);
    console.log(`✅ Successful: ${successfulQueries.length}`);
    console.log(`❌ Failed: ${failedQueries.length}`);
    
    if (successfulQueries.length > 0) {
      const durations = successfulQueries.map(r => r.duration);
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const minDuration = Math.min(...durations);
      const maxDuration = Math.max(...durations);
      
      console.log(`\n⏱️  TIMING STATISTICS:`);
      console.log(`   Average: ${Math.round(avgDuration * 100) / 100}ms`);
      console.log(`   Fastest: ${minDuration}ms`);
      console.log(`   Slowest: ${maxDuration}ms`);
      
      // Show slowest queries
      const slowestQueries = successfulQueries
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5);
      
      console.log(`\n🐌 SLOWEST QUERIES:`);
      slowestQueries.forEach((query, index) => {
        console.log(`   ${index + 1}. ${query.name}: ${query.duration}ms`);
      });
    }
    
    if (failedQueries.length > 0) {
      console.log(`\n❌ FAILED QUERIES:`);
      failedQueries.forEach(query => {
        console.log(`   - ${query.name}: ${query.error}`);
      });
    }
  }

  /**
   * Run all stress tests
   */
  async runAllTests() {
    console.log('🚀 Starting database stress tests...\n');
    
    try {
      // Connect to database
      await dbConnection.connect();
      console.log('✅ Database connected\n');
      
      // Run test suites
      await this.testBasicQueries();
      console.log();
      
      await this.testComplexQueries();
      console.log();
      
      await this.testSearchQueries();
      console.log();
      
      await this.testConcurrentQueries();
      console.log();
      
      await this.testRepeatedQueries();
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Stress test failed:', error.message);
    } finally {
      await dbConnection.close();
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const stressTest = new DatabaseStressTest();
  stressTest.runAllTests().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { DatabaseStressTest };
