import { createRequire } from 'module';

// ES module equivalent of require for CommonJS modules
const require = createRequire(import.meta.url);

// Load environment variables
if (!process.env.NUXT_ENV_INIT) {
  try {
    const dotenv = await import('dotenv');
    dotenv.config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

// Import CommonJS modules
const dbConnection = require('../../scraper/database/connection');

async function checkDatabase() {
  console.log('🔍 Checking database status...\n');
  
  try {
    // Connect to database
    await dbConnection.connect();
    console.log('✅ Database connected successfully');
    
    // Check total records
    const countResult = await dbConnection.query('SELECT COUNT(*) as total FROM used_cars');
    const totalRecords = parseInt(countResult.rows[0].total);
    console.log(`📊 Total records: ${totalRecords.toLocaleString()}`);
    
    if (totalRecords === 0) {
      console.log('⚠️  No data found. Run sample data generation first:');
      console.log('   npm run generate-test-data');
      return;
    }
    
    // Check makers distribution
    const makersResult = await dbConnection.query(`
      SELECT maker, COUNT(*) as count 
      FROM used_cars 
      WHERE maker IS NOT NULL 
      GROUP BY maker 
      ORDER BY count DESC 
      LIMIT 10
    `);
    
    console.log('\n🏭 Top 10 Makers:');
    makersResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.maker}: ${parseInt(row.count).toLocaleString()} cars`);
    });
    
    // Check price distribution
    const priceResult = await dbConnection.query(`
      SELECT 
        MIN(price) as min_price,
        MAX(price) as max_price,
        AVG(price) as avg_price,
        COUNT(*) as total_with_price
      FROM used_cars 
      WHERE price IS NOT NULL
    `);
    
    const priceStats = priceResult.rows[0];
    console.log('\n💰 Price Statistics:');
    console.log(`   Min: ${parseFloat(priceStats.min_price).toFixed(0)}万円`);
    console.log(`   Max: ${parseFloat(priceStats.max_price).toFixed(0)}万円`);
    console.log(`   Average: ${parseFloat(priceStats.avg_price).toFixed(0)}万円`);
    console.log(`   Records with price: ${parseInt(priceStats.total_with_price).toLocaleString()}`);
    
    // Check recent records
    const recentResult = await dbConnection.query(`
      SELECT car_name, maker, model, price, created_at
      FROM used_cars 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log('\n🕒 Recent Records:');
    recentResult.rows.forEach((row, index) => {
      const createdAt = new Date(row.created_at).toLocaleString();
      console.log(`   ${index + 1}. ${row.car_name} (${row.maker} ${row.model}) - ${row.price}万円 [${createdAt}]`);
    });
    
    // Check database health
    const healthResult = await dbConnection.query('SELECT NOW() as current_time, version() as version');
    const health = healthResult.rows[0];
    console.log('\n🏥 Database Health:');
    console.log(`   Current time: ${new Date(health.current_time).toLocaleString()}`);
    console.log(`   Version: ${health.version.split(' ').slice(0, 2).join(' ')}`);
    
    console.log('\n✅ Database check completed successfully!');
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    process.exit(1);
  } finally {
    await dbConnection.close();
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  checkDatabase().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { checkDatabase };
