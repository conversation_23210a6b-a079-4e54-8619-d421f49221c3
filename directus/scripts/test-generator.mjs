import { generateCarRecord } from './generate-sample-data.mjs';

// Test the generator by creating a few sample records
console.log('🧪 Testing sample data generator...\n');

for (let i = 1; i <= 5; i++) {
  console.log(`--- Sample Record ${i} ---`);
  const record = generateCarRecord(i);

  // Display key fields
  console.log(`Car: ${record.car_name}`);
  console.log(`Maker: ${record.maker} | Model: ${record.model}`);
  console.log(`Year: ${record.model_year} | Price: ${record.price}万円 (${record.price_range})`);
  console.log(`Engine: ${record.displacement}cc ${record.engine_type} | ${record.driving_system}`);
  console.log(`Mileage: ${record.mileage.toLocaleString()}km | Mission: ${record.mission}`);
  console.log(`Region: ${record.region} | Handle: ${record.handle}`);
  console.log(`Safety: ${record.safe_equipment.split('\n').slice(0, 2).join(', ')}...`);
  console.log(`Source: ${record.sourceURL}`);
  console.log('');
}

console.log('✅ Generator test completed successfully!');
console.log('\nTo generate actual data:');
console.log('npm run generate-test-data    # 10,000 records');
console.log('npm run generate-sample-data  # 1,000,000 records');
