# Database Scripts

This directory contains scripts for generating sample data and testing the used car sales database.

## Generate Sample Data

### Quick Start

Generate 1 million sample car records:

```bash
node scripts/generate-sample-data.js
```

### Custom Parameters

```bash
# Generate specific number of records
node scripts/generate-sample-data.js 100000

# Generate with custom batch size
node scripts/generate-sample-data.js 1000000 500

# Generate smaller test dataset
node scripts/generate-sample-data.js 10000 100
```

### Parameters

1. **Total Records** (default: 1,000,000)
   - Number of car records to generate
   - Must be a positive integer

2. **Batch Size** (default: 1,000)
   - Number of records to insert per database transaction
   - Larger batches = faster insertion but more memory usage
   - Recommended: 500-2000 for optimal performance

### Sample Data Features

The generator creates realistic car data based on actual CarSensor patterns:

#### **Car Information**
- **Makers**: 31 popular brands (Toyota, Honda, Mercedes-Benz, BMW, etc.)
- **Models**: Brand-specific model names (Prius, Civic, A-Class, etc.)
- **Car Names**: Generated with realistic grades and options
- **Model Years**: 1990-2024 range

#### **Technical Specifications**
- **Displacement**: Common engine sizes (660cc to 6000cc)
- **Driving Systems**: 2WD, 4WD, AWD, FF, FR, MR, RR
- **Engine Types**: Gasoline, Diesel, Hybrid, Electric, Plugin Hybrid
- **Transmissions**: Various AT, MT, CVT configurations

#### **Condition & History**
- **Mileage**: Realistic ranges (1,000-200,000 km)
- **Fix History**: None, Minor, Repair history variations
- **Vehicle Inspection**: Valid Japanese inspection dates
- **Insurance/Maintenance**: Warranty and service status

#### **Pricing**
- **Price**: Realistic ranges from 10万円 to 2000万円+
- **Price Ranges**: Categorized brackets matching CarSensor format

#### **Equipment**
- **Safety**: ABS, Airbags, Stability control, etc.
- **Comfort**: Navigation, Audio, ETC, Air conditioning
- **Interior**: Leather seats, Power windows, Keyless entry
- **Exterior**: Alloy wheels, Fog lights, Sunroof, Aero parts

#### **Location & Source**
- **Regions**: Major Japanese prefectures
- **Source**: CarSensor format with unique URLs
- **Images**: Sample image URLs

### Performance

Expected performance on typical hardware:
- **Rate**: ~1,000-2,000 records/second
- **1 Million Records**: ~8-15 minutes
- **Memory Usage**: ~50-100MB per batch

### Database Requirements

Ensure your database is configured with:
- Sufficient connection pool size
- Adequate disk space (1M records ≈ 500MB-1GB)
- Proper indexes (automatically created by schema)

### Error Handling

The script includes robust error handling:
- Continues processing if individual batches fail
- Reports progress and error counts
- Provides ETA and performance metrics
- Graceful database connection management

### Examples

```bash
# Quick test with 1,000 records
node scripts/generate-sample-data.js 1000

# Production dataset
node scripts/generate-sample-data.js 1000000

# High-performance batch processing
node scripts/generate-sample-data.js 1000000 2000

# Memory-conservative processing
node scripts/generate-sample-data.js 1000000 500
```

### Monitoring

The script provides real-time progress updates:

```
🚀 Starting generation of 1,000,000 sample car records...
📦 Batch size: 1,000
✅ Database connected
📊 Progress: 10.0% (100,000/1,000,000) | Rate: 1,234/sec | ETA: 729s
📊 Progress: 20.0% (200,000/1,000,000) | Rate: 1,456/sec | ETA: 549s
...
🎉 Generation completed!
📈 Total records processed: 1,000,000
⏱️  Total time: 678s
📊 Average rate: 1,475/sec
```

### Troubleshooting

**Slow Performance**
- Reduce batch size to 500-1000
- Check database connection pool settings
- Ensure adequate system resources

**Memory Issues**
- Reduce batch size to 100-500
- Monitor system memory usage
- Consider running in smaller chunks

**Database Errors**
- Verify database connection settings
- Check disk space availability
- Ensure proper permissions

**Connection Timeouts**
- Increase DB_CONNECTION_TIMEOUT in environment
- Reduce batch size for more frequent commits
- Check network stability

## Database Testing

### Database Status Check

Check database connectivity and data overview:

```bash
npm run check-db
```

This provides:
- Total record count
- Top makers distribution
- Price statistics
- Recent records sample
- Database health status

### Stress Testing

Run basic stress tests:

```bash
npm run stress-test-db
```

Run intensive stress tests:

```bash
npm run stress-test-db-heavy
```

### Test Categories

The stress test script runs comprehensive database performance tests:

#### **Basic Queries**
- Record counting
- Simple SELECT with LIMIT
- Single-field filtering (maker, price, mileage)
- Basic WHERE conditions

#### **Complex Queries**
- GROUP BY with aggregations (COUNT, AVG, MIN, MAX)
- Multi-table statistics
- CASE statements for data categorization
- JSONB equipment searches

#### **Search Queries**
- ILIKE pattern matching
- Multi-field searches
- Equipment text searches
- Combined condition filtering

#### **Concurrent Testing**
- Multiple simultaneous queries
- Connection pool stress testing
- Parallel query execution
- Resource contention analysis

#### **Consistency Testing**
- Repeated query execution
- Performance variance analysis
- Cache behavior testing
- Timing consistency validation

### Configuration

Control test parameters with environment variables:

```bash
# Number of concurrent queries (default: 5)
CONCURRENT_QUERIES=10 npm run stress-test-db

# Number of iterations for repeated tests (default: 10)
TEST_ITERATIONS=20 npm run stress-test-db

# Combined intensive testing
CONCURRENT_QUERIES=10 TEST_ITERATIONS=20 npm run stress-test-db
```

### Performance Report

The script generates a comprehensive performance report:

```
📊 PERFORMANCE REPORT
==================================================
📈 Total queries executed: 45
✅ Successful: 44
❌ Failed: 1

⏱️  TIMING STATISTICS:
   Average: 125.67ms
   Fastest: 12ms
   Slowest: 1,234ms

🐌 SLOWEST QUERIES:
   1. Price statistics by region: 1,234ms
   2. Group by maker with count: 567ms
   3. Equipment search (JSONB): 234ms
```

### Use Cases

**Performance Monitoring**
- Baseline performance measurement
- Regression testing after schema changes
- Index effectiveness validation

**Capacity Planning**
- Concurrent user simulation
- Resource utilization analysis
- Scalability assessment

**Query Optimization**
- Slow query identification
- Index usage analysis
- Performance bottleneck detection
