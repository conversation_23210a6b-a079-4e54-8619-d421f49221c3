// Utility function to validate image URLs
export function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;

  // Remove whitespace
  url = url.trim();

  // Check if empty after trimming
  if (!url) return false;

  // Check for basic URL structure
  // Allow both http/https and protocol-relative URLs (//)
  const urlPattern = /^(https?:\/\/|\/\/)[^\s<>"{}|\\^`[\]]+$/i;
  if (!urlPattern.test(url)) return false;

  // Filter out known problematic patterns
  const problematicPatterns = [
    /animation_M\.gif/i,
    /placeholder/i,
    /no-image/i,
    /noimage/i,
    /loading/i,
    /spinner/i,
    /blank\.(jpg|png|gif)/i
  ];

  for (const pattern of problematicPatterns) {
    if (pattern.test(url)) return false;
  }

  return true;
}

// Utility function to format numbers with comma separators
export function formatNumber(value: number | string | null | undefined): string {
  if (value === null || value === undefined) return '-';
  
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '-';
  
  return num.toLocaleString('ja-JP');
}


export const limitItems = [
  { label: "50 台", value: 50 },
  { label: "100 台", value: 100 },
  { label: "250 台", value: 250 },
  { label: "500 台", value: 500 }
];

// Filter items
export const transmissionItems = [
  { label: "AT/CVT", value: "AT" },
  { label: "MT", value: "MT" },
  { label: "指定なし", value: "" },
];

export const drivetrainItems = [
  { label: "2WD", value: "2WD" },
  { label: "4WD", value: "4WD" },
  { label: "指定なし", value: "" },
];

export const accidentHistoryItems = [
  { label: "なし", value: "なし" },
  { label: "あり", value: "あり" },
  { label: "指定なし", value: "" },
];

export const steeringItems = [
  { label: "右", value: "右" },
  { label: "左", value: "左" },
  { label: "指定なし", value: "" },
];

export const engineTypeItems = [
  { label: "ガソリン", value: "ガソリン" },
  { label: "ディーゼル", value: "ディーゼル" },
  { label: "ハイブリッド", value: "ハイブリッド" },
  { label: "電気", value: "電気" },
  { label: "その他", value: "その他" },
  { label: "指定なし", value: "" },
];

export const sortItems = [
  { label: "本体価格安い順", value: "price_asc" },
  { label: "本体価格高い順", value: "price_desc" },
  { label: "年式新しい順", value: "model_newest" },
  { label: "年式古い順", value: "model_oldest" },
  { label: "新着順", value: "scraped_desc" },
  { label: "古い順", value: "scraped_asc" },
];
