{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "generate-sample-data": "DB_HOST=localhost node directus/scripts/generate-sample-data.mjs", "generate-test-data": "DB_HOST=localhost node directus/scripts/generate-sample-data.mjs 10000 100", "stress-test-db": "DB_HOST=localhost node directus/scripts/stress-test-database.mjs", "stress-test-db-heavy": "CONCURRENT_QUERIES=10 TEST_ITERATIONS=20 node scripts/stress-test-database.mjs", "check-db": "DB_HOST=localhost node directus/scripts/check-database.mjs", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f nuxt-app", "docker:build": "docker-compose build --no-cache"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@nuxt/ui-pro": "^3.3.2", "@vueuse/core": "^13.7.0", "@vueuse/nuxt": "^13.7.0", "add": "^2.0.6", "apexcharts": "^5.3.4", "date-fns": "^4.1.0", "nuxt": "^4.0.3", "nuxt-easy-lightbox": "^1.1.0", "pg": "^8.16.3", "playwright": "^1.54.2", "resend": "^6.0.1", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}