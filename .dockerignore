# Node modules
node_modules/
scraper/node_modules/

# Build outputs
.nuxt/
.output/
dist/

# Development files
.env
.env.local
.env.*.local

# Git
.git/
.gitignore

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Scraper output (will be mounted as volumes)
scraper/output/
scraper/output_merge/

# Documentation
*.md
docs/

# Docker files (not needed in container)
docker-compose*.yml
Dockerfile*

# GitHub Actions
.github/

# Production environment file
.env.prod
