name: Build and Push Docker Image

on:
  push:
    branches: [ main, dokploy ]
    tags: [ 'v*' ]
    paths:
      - 'server/**'
      - 'pages/**'
      - 'scraper/**'
      - 'assets/**'
      - 'public/**'
      - 'app.vue'
      - 'nuxt.config.ts'
      - 'package*.json'
      - 'Dockerfile'
      - '.dockerignore'
      - 'tsconfig.json'
  pull_request:
    branches: [ main ]
    paths:
      - 'server/**'
      - 'pages/**'
      - 'scraper/**'
      - 'assets/**'
      - 'public/**'
      - 'app.vue'
      - 'nuxt.config.ts'
      - 'package*.json'
      - 'Dockerfile'
      - '.dockerignore'
      - 'tsconfig.json'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  check-changes:
    runs-on: ubuntu-latest
    outputs:
      should-build: ${{ steps.changes.outputs.should-build }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 2

    - name: Check for relevant changes
      id: changes
      run: |
        # Check if this is a tag push (always build tags)
        if [[ "${GITHUB_REF}" == refs/tags/* ]]; then
          echo "should-build=true" >> $GITHUB_OUTPUT
          echo "Building because this is a tag push"
          exit 0
        fi
        
        # For regular pushes, check if relevant files changed
        if git diff --name-only HEAD~1 HEAD | grep -E '^(server/|pages/|scraper/|assets/|public/|app\.vue|nuxt\.config\.ts|package.*\.json|Dockerfile|\.dockerignore|tsconfig\.json)'; then
          echo "should-build=true" >> $GITHUB_OUTPUT
          echo "Building because relevant code files changed"
        else
          echo "should-build=false" >> $GITHUB_OUTPUT
          echo "Skipping build - only config files changed"
        fi

  build:
    needs: check-changes
    if: needs.check-changes.outputs.should-build == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max