#!/usr/bin/env node

/**
 * Test script for enhanced scraper functionality
 * Tests retry logic, email alerts, and progress tracking
 */

const { NotificationService } = require('./lib/notification-service');
const { RetryService } = require('./lib/retry-service');
const fs = require('fs');
const path = require('path');

// Test configuration
const testConfig = {
  email: {
    enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.resend.com',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    },
    fromEmail: process.env.FROM_EMAIL,
    toEmails: process.env.TO_EMAILS?.split(',').map(e => e.trim()) || [],
    alertCooldownMinutes: 1 // Short cooldown for testing
  }
};

console.log('🧪 Starting Enhanced Scraper Tests\n');

// Test 1: Retry Service
async function testRetryService() {
  console.log('📋 Testing Retry Service...');
  
  // Test successful operation after retries
  let attempt = 0;
  try {
    const result = await RetryService.withRetry(async () => {
      attempt++;
      if (attempt < 3) {
        throw new Error('Simulated temporary failure');
      }
      return 'Success!';
    }, RetryService.DETAIL_RETRY_CONFIG, {
      shouldRetry: (error) => RetryService.isRetryableError(error) || attempt < 3,
      onRetry: (error, attemptNum) => {
        console.log(`  🔄 Retry ${attemptNum}: ${error.message}`);
      }
    });
    
    console.log(`  ✅ Retry service working: ${result} (after ${attempt} attempts)\n`);
  } catch (error) {
    console.error(`  ❌ Retry service failed: ${error.message}\n`);
  }
}

// Test 2: Error Classification
async function testErrorClassification() {
  console.log('📋 Testing Error Classification...');
  
  const testErrors = [
    { error: { message: 'ETIMEDOUT' }, expectedRetryable: true },
    { error: { message: 'Network timeout' }, expectedRetryable: true },
    { error: { message: '502 Bad Gateway' }, expectedRetryable: true },
    { error: { message: 'Browser crashed' }, expectedRetryable: false },
    { error: { message: 'Authentication failed' }, expectedRetryable: false },
  ];
  
  for (const test of testErrors) {
    const isRetryable = RetryService.isRetryableError(test.error);
    const isCritical = RetryService.isCriticalError(test.error);
    
    if (isRetryable === test.expectedRetryable) {
      console.log(`  ✅ "${test.error.message}" classified correctly (retryable: ${isRetryable}, critical: ${isCritical})`);
    } else {
      console.log(`  ❌ "${test.error.message}" misclassified (expected retryable: ${test.expectedRetryable}, got: ${isRetryable})`);
    }
  }
  console.log('');
}

// Test 3: Notification Service
async function testNotificationService() {
  console.log('📋 Testing Notification Service...');
  
  const notificationService = new NotificationService();
  
  if (!testConfig.email.enabled || !testConfig.email.smtp.auth.user || !testConfig.email.fromEmail) {
    console.log('  ⏭️  Email not configured, skipping email tests\n');
    return;
  }
  
  try {
    notificationService.initialize(testConfig.email);
    console.log('  ✅ Notification service initialized');
    
    // Test alert sending
    const testAlert = {
      type: 'brand_failure',
      brand: 'TestBrand',
      message: 'This is a test alert from the enhanced scraper',
      error: 'Simulated test error',
      attemptNumber: 1,
      totalAttempts: 3
    };
    
    const result = await notificationService.sendAlert(testAlert);
    if (result) {
      console.log('  ✅ Test alert sent successfully');
    } else {
      console.log('  ❌ Test alert failed to send');
    }
  } catch (error) {
    console.error(`  ❌ Notification service error: ${error.message}`);
  }
  console.log('');
}

// Test 4: Progress Tracking
async function testProgressTracking() {
  console.log('📋 Testing Progress Tracking...');
  
  const progressFile = path.join(__dirname, 'test_progress.json');
  
  try {
    // Create test progress data
    const progressData = {
      processedBrands: ['TestBrand1', 'TestBrand2'],
      failedBrands: ['FailedBrand1'],
      totalCarsScraped: 150,
      startTime: new Date().toISOString(),
      lastSavedAt: new Date().toISOString()
    };
    
    // Write progress file
    fs.writeFileSync(progressFile, JSON.stringify(progressData, null, 2));
    console.log('  ✅ Progress file created');
    
    // Read and verify progress file
    const loadedData = JSON.parse(fs.readFileSync(progressFile, 'utf8'));
    if (loadedData.totalCarsScraped === 150) {
      console.log('  ✅ Progress file read correctly');
    } else {
      console.log('  ❌ Progress file data mismatch');
    }
    
    // Cleanup
    fs.unlinkSync(progressFile);
    console.log('  ✅ Test progress file cleaned up');
    
  } catch (error) {
    console.error(`  ❌ Progress tracking error: ${error.message}`);
  }
  console.log('');
}

// Test 5: Configuration Validation
async function testConfiguration() {
  console.log('📋 Testing Configuration...');
  
  const requiredEnvVars = [
    'SCRAPER_BRAND_MAX_ATTEMPTS',
    'SCRAPER_PAGE_MAX_ATTEMPTS', 
    'SCRAPER_DETAIL_MAX_ATTEMPTS',
    'SCRAPER_BACKOFF_MULTIPLIER'
  ];
  
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value && !isNaN(parseFloat(value))) {
      console.log(`  ✅ ${envVar}: ${value}`);
    } else {
      console.log(`  ⚠️  ${envVar}: using default (not set or invalid)`);
    }
  }
  
  console.log(`  📧 Email notifications: ${testConfig.email.enabled ? 'enabled' : 'disabled'}`);
  if (testConfig.email.enabled) {
    console.log(`  📧 SMTP Host: ${testConfig.email.smtp.host}:${testConfig.email.smtp.port}`);
    console.log(`  📧 From Email: ${testConfig.email.fromEmail || 'not configured'}`);
    console.log(`  📧 To Emails: ${testConfig.email.toEmails.length} recipients`);
  }
  console.log('');
}

// Run all tests
async function runTests() {
  try {
    await testRetryService();
    await testErrorClassification();
    await testNotificationService();
    await testProgressTracking();
    await testConfiguration();
    
    console.log('🎉 All tests completed!\n');
    
    console.log('💡 To run the enhanced scraper:');
    console.log('   npm run scrape:enhanced');
    console.log('\n💡 For limited testing:');
    console.log('   MAX_BRANDS_TO_PROCESS=2 npm run scrape:enhanced');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Load environment variables
if (!process.env.NUXT_ENV_INIT) {
  try {
    require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

runTests();