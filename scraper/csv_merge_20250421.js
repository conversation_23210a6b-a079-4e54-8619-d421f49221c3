const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const fastcsv = require('fast-csv');

async function mergeCSVFilesByFilename(inputDir) {
  try {
    // 1. CSV一覧取得＆ソート
    const files = fs.readdirSync(inputDir)
      .filter(f => path.extname(f).toLowerCase() === '.csv')
      .sort();
    if (files.length === 0) {
      console.log('CSVファイルが見つかりません');
      return;
    }

    // 2. ファイル名プレフィックス（makerName）ごとにファイルをグループ化
    //    例: "メルセデスベンツ_2025-04-21.csv" → makerName="メルセデスベンツ"
    const groups = {};       // { makerName: [ 'ファイルパス1', 'ファイルパス2', ... ] }
    let header = null;       // 共通ヘッダー

    for (const file of files) {
      const filePath = path.join(inputDir, file);

      // ファイル名からプレフィックスを抽出
      //   日付 (YYYY-MM-DD 以降) の前で分割
      const basename = path.basename(file, '.csv');
      const makerName = basename.split('_')[0] || 'unknown';

      // ヘッダーは最初のファイルからだけ取得
      if (!header) {
        const firstContent = fs.readFileSync(filePath, 'utf8');
        const [firstLine] = parse(firstContent, { to_line: 1, skip_empty_lines: true });
        header = firstLine;
      }

      // グループに追加
      if (!groups[makerName]) groups[makerName] = [];
      groups[makerName].push(filePath);

      console.log(`グループ登録: ${makerName} ← ${file}`);
    }

    // 3. グループごとにマージ出力
    const today = new Date().toISOString().slice(0, 10);  // YYYY‑MM‑DD
    for (const [makerName, filePaths] of Object.entries(groups)) {
      // 出力用の安全なメーカー名
      const safeMaker = makerName
        .replace(/[\/\\?%*:|"<>\s]+/g, '_')
        .replace(/_+/g, '_');

      const outputFileName = `${safeMaker}_${today}.csv`;
      const outputFilePath = path.join(inputDir+"_merge", outputFileName);

      // 書き込みストリーム（BOM付き）
      const ws = fs.createWriteStream(outputFilePath, { encoding: 'utf8' });
      ws.write('\uFEFF');

      const csvStream = fastcsv.format({ headers: header, writeHeaders: true });
      csvStream.pipe(ws).on('finish', () => {
        console.log(`マージ完了: ${outputFileName} （${filePaths.length} ファイル）`);
      });

      // 各ファイルからデータを読み込んで書き出し
      for (const fp of filePaths) {
        const content = fs.readFileSync(fp, 'utf8');
        const records = parse(content, {
          columns: header,
          from_line: 2,
          skip_empty_lines: true
        });
        records.forEach(r => csvStream.write(r));
        console.log(`  → 読み込み: ${path.basename(fp)} （${records.length} 件）`);
      }

      csvStream.end();
    }

  } catch (err) {
    console.error('エラーが発生しました:', err);
  }
}

// 使用例
mergeCSVFilesByFilename(path.resolve(__dirname, 'output'));
