const dbConnection = require('./connection');

class BatchInsert {
    constructor() {
        this.batchSize = parseInt(process.env.BATCH_SIZE) || 100;
    }

    /**
     * Parse equipment strings into JSON arrays
     */
    parseEquipment(equipmentString) {
        if (!equipmentString || equipmentString.trim() === '') {
            return null;
        }

        // Split by newlines and filter out empty strings
        const items = equipmentString
            .split('\n')
            .map(item => item.trim())
            .filter(item => item.length > 0);

        return items.length > 0 ? JSON.stringify(items) : null;
    }

    /**
     * Parse price from string (remove 万円 and convert to number)
     */
    parsePrice(priceString) {
        if (!priceString || priceString === '') return null;

        // Remove 万円 and other non-numeric characters except decimal point
        const numericPrice = priceString.toString().replace(/[万円,]/g, '').trim();
        const parsed = parseFloat(numericPrice);

        return isNaN(parsed) ? null : parsed;
    }

    /**
     * Parse mileage (ensure it's a number)
     */
    parseMileage(mileageString) {
        if (!mileageString || mileageString === '') return null;

        const parsed = parseInt(mileageString.toString().replace(/[,]/g, ''));
        return isNaN(parsed) ? null : parsed;
    }

    /**
     * Parse displacement (ensure it's a number)
     */
    parseDisplacement(displacementString) {
        if (!displacementString || displacementString === '') return null;

        const parsed = parseInt(displacementString.toString().replace(/[CC,]/gi, '').trim());
        return isNaN(parsed) ? null : parsed;
    }

    /**
     * Parse model year from string to date
     */
    parseModelYear(yearString) {
        if (!yearString || yearString === '') return null;

        // Handle various date formats
        const dateMatch = yearString.match(/(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})/);
        if (dateMatch) {
            const [, year, month, day] = dateMatch;
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }

        // Handle year only
        const yearMatch = yearString.match(/(\d{4})/);
        if (yearMatch) {
            return `${yearMatch[1]}-01-01`;
        }

        return null;
    }

    /**
     * Transform CSV row data to database format
     */
    transformCarData(carData) {
        return {
            car_name: carData.car_name || null,
            detail: carData.detail || null,
            maker: carData.maker || null,
            model: carData.model || null,
            model_year: this.parseModelYear(carData.model_year),
            displacement: this.parseDisplacement(carData.displacement),
            driving_system: carData.driving_system || null,
            engine_type: carData.engine_type || null,
            mission: carData.mission || null,
            mission_type: carData.Mission_type || null,
            handle: carData.handle || null,
            fix_history: carData.fix_history || null,
            mileage: this.parseMileage(carData.mileage),
            vehicle_inspection: carData.vehicle_inspection || null,
            insurance: carData.insurance || null,
            maintenance: carData.maintenance || null,
            price: this.parsePrice(carData.price),
            price_range: carData.price_range || null,
            price_range_500: carData.price_range_500 || null,
            price_range_1000: carData.price_range_1000 || null,
            region: carData.region || null,
            source: carData.source || null,
            source_url: carData.sourceURL || null,
            image: carData.image || null,
            safe_equipment: this.parseEquipment(carData.safe_equipment),
            comfort_equipment: this.parseEquipment(carData.comfort_equipment),
            interior_equipment: this.parseEquipment(carData.interia_equipment), // Note: typo in original CSV
            exterior_equipment: this.parseEquipment(carData.exteria_equipment), // Note: typo in original CSV
            scraped_at: new Date()
        };
    }

    /**
     * Perform batch insert of car data (allows duplicates)
     */
    async upsertCars(carsData) {
        if (!Array.isArray(carsData) || carsData.length === 0) {
            return { inserted: 0, updated: 0, errors: [] };
        }

        console.log(`Inserting ${carsData.length} cars in batches of ${this.batchSize}...`);

        let totalInserted = 0;
        let totalUpdated = 0;
        const errors = [];
        const totalBatches = Math.ceil(carsData.length / this.batchSize);

        // Process in batches
        for (let i = 0; i < carsData.length; i += this.batchSize) {
            const batch = carsData.slice(i, i + this.batchSize);
            const batchNumber = Math.floor(i / this.batchSize) + 1;

            try {
                const result = await this.upsertBatch(batch);
                totalInserted += result.inserted;
                totalUpdated += result.updated;

                // Only log progress for larger operations (every 10 batches or final batch)
                if (totalBatches > 10 && (batchNumber % 10 === 0 || batchNumber === totalBatches)) {
                    console.log(`Progress: ${batchNumber}/${totalBatches} batches (${totalInserted} inserted)`);
                }
            } catch (error) {
                console.error(`Batch ${batchNumber} failed:`, error.message);
                errors.push({
                    batch: batchNumber,
                    error: error.message,
                    data: batch
                });
            }
        }

        console.log(`Completed: ${totalInserted} inserted${errors.length > 0 ? `, ${errors.length} errors` : ''}`);

        return {
            inserted: totalInserted,
            updated: totalUpdated,
            errors: errors
        };
    }

    /**
     * Insert a single batch of cars (no upsert, allows duplicates)
     */
    async upsertBatch(batch) {
        const transformedBatch = batch.map(car => this.transformCarData(car));

        // Filter out cars without required fields
        const validCars = transformedBatch.filter(car => car.car_name);

        if (validCars.length === 0) {
            return { inserted: 0, updated: 0 };
        }

        const columns = [
            'car_name', 'detail', 'maker', 'model', 'model_year', 'displacement',
            'driving_system', 'engine_type', 'mission', 'mission_type', 'handle',
            'fix_history', 'mileage', 'vehicle_inspection', 'insurance', 'maintenance',
            'price', 'price_range', 'price_range_500', 'price_range_1000', 'region', 'source', 'source_url', 'image',
            'safe_equipment', 'comfort_equipment', 'interior_equipment', 'exterior_equipment',
            'scraped_at'
        ];

        // Build the VALUES clause with placeholders
        const valuesClauses = [];
        const values = [];
        let paramIndex = 1;

        for (const car of validCars) {
            const carValues = columns.map(col => {
                values.push(car[col]);
                return `$${paramIndex++}`;
            });
            valuesClauses.push(`(${carValues.join(', ')})`);
        }

        const query = `
            INSERT INTO used_cars (${columns.join(', ')})
            VALUES ${valuesClauses.join(', ')}
            RETURNING id
        `;

        const result = await dbConnection.query(query, values);

        const processed = result.rows.length;

        return {
            inserted: processed,
            updated: 0,
            processed: processed
        };
    }
}

module.exports = new BatchInsert();
