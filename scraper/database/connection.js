const { Pool } = require('pg');

// Load dotenv only if not running in Nuxt environment
if (!process.env.NUXT_ENV_INIT) {
    try {
        require('dotenv').config();
    } catch (error) {
        // dotenv not available, environment variables should be set externally
        console.log('Note: dotenv not available, using system environment variables');
    }
}

class DatabaseConnection {
    constructor() {
        this.pool = null;
        this.isConnected = false;
    }

    /**
     * Initialize the database connection pool
     */
    async connect() {
        if (this.isConnected) {
            return this.pool;
        }

        try {
            this.pool = new Pool({
                host: process.env.DB_HOST || 'localhost',
                port: process.env.DB_PORT || 26257,
                database: process.env.DB_NAME || 'usedcarsales',
                user: process.env.DB_USER || 'root',
                password: process.env.DB_PASSWORD || '',
                ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
                max: parseInt(process.env.DB_POOL_MAX) || 20,
                idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
                connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 10000,
            });

            // Test the connection
            const client = await this.pool.connect();
            await client.query('SELECT 1');
            client.release();

            this.isConnected = true;
            console.log('✅ Database connected successfully');
            return this.pool;
        } catch (error) {
            console.error('❌ Database connection failed:', error.message);
            throw error;
        }
    }

    /**
     * Get a client from the pool
     */
    async getClient() {
        if (!this.isConnected) {
            await this.connect();
        }
        return this.pool.connect();
    }

    /**
     * Execute a query with automatic client management
     */
    async query(text, params = []) {
        if (!this.isConnected) {
            await this.connect();
        }

        const client = await this.pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        } finally {
            client.release();
        }
    }

    /**
     * Execute multiple queries in a transaction
     */
    async transaction(queries) {
        if (!this.isConnected) {
            await this.connect();
        }

        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');

            const results = [];
            for (const { text, params } of queries) {
                const result = await client.query(text, params);
                results.push(result);
            }

            await client.query('COMMIT');
            return results;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * Close the database connection pool
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            this.isConnected = false;
            console.log('🔌 Database connection closed');
        }
    }

    /**
     * Check if the database is healthy
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT NOW() as current_time');
            return {
                status: 'healthy',
                timestamp: result.rows[0].current_time,
                connected: this.isConnected
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                connected: this.isConnected
            };
        }
    }
}

// Create a singleton instance
const dbConnection = new DatabaseConnection();

module.exports = dbConnection;
