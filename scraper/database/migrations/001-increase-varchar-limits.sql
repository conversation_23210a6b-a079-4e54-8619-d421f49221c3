-- Migration to increase VARCHAR limits to prevent data truncation
-- This migration can be run on existing databases

-- Increase VARCHAR limits for better data safety
ALTER TABLE used_cars 
    ALTER COLUMN maker TYPE VARCHAR(255),
    ALTER COLUMN model TYPE VARCHAR(255),
    ALTER COLUMN driving_system TYPE VARCHAR(100),
    ALTER COLUMN engine_type TYPE VARCHAR(100),
    ALTER COLUMN mission TYPE VARCHAR(255),
    ALTER COLUMN mission_type TYPE VARCHAR(20),
    ALTER COLUMN handle TYPE VARCHAR(20),
    ALTER COLUMN fix_history TYPE VARCHAR(255),
    ALTER COLUMN vehicle_inspection TYPE VARCHAR(255),
    ALTER COLUMN insurance TYPE VARCHAR(255),
    ALTER COLUMN maintenance TYPE VARCHAR(255),
    ALTER COLUMN price_range TYPE VARCHAR(100),
    ALTER COLUMN price_range_500 TYPE VARCHAR(100),
    ALTER COLUMN price_range_1000 TYPE VARCHAR(100),
    ALTER COLUMN region TYPE VARCHAR(255),
    ALTER COLUMN source TYPE VARCHAR(255);

-- Note: These changes are backward compatible and safe to run on existing data
-- The increased limits provide better safety against data truncation without
-- significant memory overhead in modern PostgreSQL/CockroachDB systems