-- Used Car Sales Database - Complete Schema
-- Consolidated initialization and schema creation
-- Safe for multiple runs - uses IF NOT EXISTS for all operations

-- Create database (safe for restarts)
CREATE DATABASE IF NOT EXISTS usedcarsales;

-- Connect to the database
\c usedcarsales;

-- Used Car Sales Database Schema
-- Based on the CSV structure from the scraping scripts

CREATE TABLE IF NOT EXISTS used_cars (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Basic car information
    car_name VARCHAR(500) NOT NULL,
    detail TEXT,
    maker VARCHAR(255),
    model VARCHAR(255),
    model_year DATE,

    -- Technical specifications
    displacement INTEGER, -- in CC
    driving_system VARCHAR(100), -- 2WD, 4WD, etc.
    engine_type VARCHAR(100), -- ガソリン, ディーゼル, etc.
    mission VARCHAR(255), -- transmission details
    mission_type VARCHAR(20), -- MT, AT, CVT
    handle VARCHAR(20), -- 右, 左 (right/left hand drive)

    -- Condition and history
    fix_history VARCHAR(255), -- なし, あり, etc.
    mileage INTEGER, -- in km
    vehicle_inspection VARCHAR(255), -- 車検 information

    -- Maintenance and insurance
    insurance VARCHAR(255),
    maintenance VARCHAR(255),

    -- Pricing (enhanced with multiple price ranges)
    price DECIMAL(10,2), -- in 万円 (10,000 yen units)
    price_range VARCHAR(100), -- price range category
    price_range_500 VARCHAR(100), -- 500万円 bracket price range
    price_range_1000 VARCHAR(100), -- 1000万円 bracket price range

    -- Location and source
    region VARCHAR(255),
    source VARCHAR(255), -- カーセンサー, etc.
    source_url TEXT,

    -- Media
    image TEXT, -- image URL(s) - comma separated for multiple images

    -- Equipment (stored as JSON arrays for flexibility)
    safe_equipment JSONB,
    comfort_equipment JSONB,
    interior_equipment JSONB,
    exterior_equipment JSONB,

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    scraped_at TIMESTAMP
);

-- Indexes for common queries (safe for restarts)
CREATE INDEX IF NOT EXISTS idx_used_cars_maker ON used_cars(maker);
CREATE INDEX IF NOT EXISTS idx_used_cars_model ON used_cars(model);
CREATE INDEX IF NOT EXISTS idx_used_cars_price ON used_cars(price);
CREATE INDEX IF NOT EXISTS idx_used_cars_mileage ON used_cars(mileage);
CREATE INDEX IF NOT EXISTS idx_used_cars_model_year ON used_cars(model_year);
CREATE INDEX IF NOT EXISTS idx_used_cars_region ON used_cars(region);
CREATE INDEX IF NOT EXISTS idx_used_cars_source ON used_cars(source);
CREATE INDEX IF NOT EXISTS idx_used_cars_created_at ON used_cars(created_at);

-- Indexes for price ranges
CREATE INDEX IF NOT EXISTS idx_used_cars_price_range ON used_cars(price_range);
CREATE INDEX IF NOT EXISTS idx_used_cars_price_range_500 ON used_cars(price_range_500);
CREATE INDEX IF NOT EXISTS idx_used_cars_price_range_1000 ON used_cars(price_range_1000);

-- Index for equipment searches (GIN index for JSONB)
CREATE INDEX IF NOT EXISTS idx_used_cars_safe_equipment ON used_cars USING GIN(safe_equipment);
CREATE INDEX IF NOT EXISTS idx_used_cars_comfort_equipment ON used_cars USING GIN(comfort_equipment);
CREATE INDEX IF NOT EXISTS idx_used_cars_interior_equipment ON used_cars USING GIN(interior_equipment);
CREATE INDEX IF NOT EXISTS idx_used_cars_exterior_equipment ON used_cars USING GIN(exterior_equipment);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_used_cars_updated_at ON used_cars;
CREATE TRIGGER update_used_cars_updated_at
    BEFORE UPDATE ON used_cars
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Blacklist table for filtering out unwanted vehicles/models
CREATE TABLE IF NOT EXISTS blacklist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Type of blacklist entry
    type VARCHAR(20) NOT NULL CHECK (type IN ('maker', 'model', 'car_name_pattern', 'car_name_exact')),

    -- The value to blacklist (maker name, model name, or pattern)
    value VARCHAR(500) NOT NULL,

    -- Optional reason for blacklisting
    reason TEXT,

    -- Whether this blacklist entry is active
    is_active BOOLEAN DEFAULT true,

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    -- Ensure no duplicate active entries of the same type and value
    UNIQUE(type, value, is_active) WHERE is_active = true
);

-- Indexes for blacklist table
CREATE INDEX IF NOT EXISTS idx_blacklist_type ON blacklist(type);
CREATE INDEX IF NOT EXISTS idx_blacklist_value ON blacklist(value);
CREATE INDEX IF NOT EXISTS idx_blacklist_is_active ON blacklist(is_active);

-- Trigger for blacklist updated_at
DROP TRIGGER IF EXISTS update_blacklist_updated_at ON blacklist;
CREATE TRIGGER update_blacklist_updated_at
    BEFORE UPDATE ON blacklist
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add keyword column for simplified filtering (runs after table creation, safe for existing tables)
ALTER TABLE blacklist ADD COLUMN IF NOT EXISTS keyword VARCHAR(500);

-- Add keyword index (safe for existing tables)
CREATE INDEX IF NOT EXISTS idx_blacklist_keyword ON blacklist(keyword);

-- -- Insert a sample car record for testing (safe for restarts)
-- INSERT INTO used_cars (
--     car_name, detail, maker, model, model_year, displacement, driving_system,
--     engine_type, mission, mission_type, handle, fix_history, mileage,
--     vehicle_inspection, insurance, maintenance, price, price_range, price_range_500, price_range_1000,
--     region, source, source_url, image, safe_equipment, comfort_equipment,
--     interior_equipment, exterior_equipment, scraped_at
-- )
-- SELECT
--     'プリウス S',
--     '禁煙車、ワンオーナー車',
--     'トヨタ',
--     'プリウス',
--     '2020/1/1',
--     1800,
--     '2WD',
--     'ハイブリッド',
--     'フロアCVT',
--     'AT/CVT',
--     '右',
--     'なし',
--     35000,
--     '2025(R7)年06月',
--     '保証付',
--     '法定整備付',
--     250,
--     '201~300万円',
--     '1~500万円',
--     '1~1000万円',
--     '東京都',
--     'カーセンサー',
--     'https://www.carsensor.net/usedcar/detail/SAMPLE0000000001/index.html',
--     '//ccsrpcma.carsensor.net/CSphoto/sample/123456/sample_001L.JPG',
--     '["パワステ", "ABS", "エアバッグ：運転席/助手席", "横滑り防止装置"]',
--     '["エアコン", "パワーウィンドウ", "集中ドアロック", "カーナビ"]',
--     '["本革シート", "シートヒーター", "電動シート"]',
--     '["アルミホイール", "フォグランプ", "ルーフレール"]',
--     NOW()
-- WHERE NOT EXISTS (
--     SELECT 1 FROM used_cars WHERE source_url = 'https://www.carsensor.net/usedcar/detail/SAMPLE0000000001/index.html'
-- );

-- Sample blacklist entries (safe for restarts)
-- INSERT INTO blacklist (type, value, reason, is_active)
-- SELECT 'maker', '事故車専門店', 'Accident vehicle specialist - not suitable for general sales', true
-- WHERE NOT EXISTS (SELECT 1 FROM blacklist WHERE type = 'maker' AND value = '事故車専門店');

-- INSERT INTO blacklist (type, value, reason, is_active)
-- SELECT 'car_name_pattern', '%事故%', 'Cars with accident history in name', true
-- WHERE NOT EXISTS (SELECT 1 FROM blacklist WHERE type = 'car_name_pattern' AND value = '%事故%');

-- -- Sample keyword entries for simplified filtering
-- INSERT INTO blacklist (keyword, reason, is_active)
-- SELECT '事故車', 'Accident vehicles', true
-- WHERE NOT EXISTS (SELECT 1 FROM blacklist WHERE keyword = '事故車');

-- INSERT INTO blacklist (keyword, reason, is_active)
-- SELECT '修復歴', 'Repair history vehicles', true
-- WHERE NOT EXISTS (SELECT 1 FROM blacklist WHERE keyword = '修復歴');

-- Scraper runs tracking table
CREATE TABLE IF NOT EXISTS scraper_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Run details
    started_at TIMESTAMP NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
    
    -- Configuration used
    output_mode VARCHAR(20) NOT NULL CHECK (output_mode IN ('csv', 'database', 'both')),
    
    -- Results
    cars_processed INTEGER DEFAULT 0,
    cars_inserted INTEGER DEFAULT 0,
    cars_updated INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    
    -- Execution details
    trigger_source VARCHAR(20) NOT NULL DEFAULT 'manual' CHECK (trigger_source IN ('manual', 'cron', 'api')),
    duration_seconds INTEGER NULL,
    
    -- Error information
    error_message TEXT NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for scraper_runs table
CREATE INDEX IF NOT EXISTS idx_scraper_runs_started_at ON scraper_runs(started_at);
CREATE INDEX IF NOT EXISTS idx_scraper_runs_status ON scraper_runs(status);
CREATE INDEX IF NOT EXISTS idx_scraper_runs_trigger_source ON scraper_runs(trigger_source);

-- Trigger for scraper_runs updated_at
DROP TRIGGER IF EXISTS update_scraper_runs_updated_at ON scraper_runs;
CREATE TRIGGER update_scraper_runs_updated_at
    BEFORE UPDATE ON scraper_runs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Scraper failures table for tracking detailed failure information
CREATE TABLE IF NOT EXISTS scraper_failures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    run_id VARCHAR(255) NOT NULL,
    brand_name VARCHAR(255) NOT NULL,
    brand_url TEXT,
    error_message TEXT NOT NULL,
    error_stack TEXT,
    error_type VARCHAR(100),
    attempt_number INTEGER DEFAULT 1,
    max_attempts INTEGER DEFAULT 3,
    context JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for scraper_failures table
CREATE INDEX IF NOT EXISTS idx_scraper_failures_run_id ON scraper_failures(run_id);
CREATE INDEX IF NOT EXISTS idx_scraper_failures_brand_name ON scraper_failures(brand_name);
CREATE INDEX IF NOT EXISTS idx_scraper_failures_created_at ON scraper_failures(created_at);
CREATE INDEX IF NOT EXISTS idx_scraper_failures_error_type ON scraper_failures(error_type);

-- Trigger for scraper_failures updated_at
DROP TRIGGER IF EXISTS update_scraper_failures_updated_at ON scraper_failures;
CREATE TRIGGER update_scraper_failures_updated_at
    BEFORE UPDATE ON scraper_failures
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
