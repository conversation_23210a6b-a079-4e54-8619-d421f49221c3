const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const dataOutputManager = require('./lib/data-output-manager');
const { NotificationService } = require('./lib/notification-service');
const { RetryService } = require('./lib/retry-service');
const blacklistManager = require('./lib/blacklist-manager');
const FailureLogger = require('./lib/failure-logger');

// Load dotenv only if not running in Nuxt environment
if (!process.env.NUXT_ENV_INIT) {
  try {
    require('dotenv').config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

// Configuration from environment variables
const config = {
  retry: {
    brandMaxAttempts: parseInt(process.env.SCRAPER_BRAND_MAX_ATTEMPTS) || 5,
    pageMaxAttempts: parseInt(process.env.SCRAPER_PAGE_MAX_ATTEMPTS) || 5,
    detailMaxAttempts: parseInt(process.env.SCRAPER_DETAIL_MAX_ATTEMPTS) || 5,
    brandBaseDelayMs: parseInt(process.env.SCRAPER_BRAND_BASE_DELAY_MS) || 5000,
    pageBaseDelayMs: parseInt(process.env.SCRAPER_PAGE_BASE_DELAY_MS) || 2000,
    detailBaseDelayMs: parseInt(process.env.SCRAPER_DETAIL_BASE_DELAY_MS) || 1000,
    backoffMultiplier: parseFloat(process.env.SCRAPER_BACKOFF_MULTIPLIER) || 3
  },
  email: {
    enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.resend.com',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    },
    fromEmail: process.env.FROM_EMAIL,
    toEmails: process.env.TO_EMAILS?.split(',').map(e => e.trim()) || [],
    alertCooldownMinutes: parseInt(process.env.EMAIL_ALERT_COOLDOWN_MINUTES) || 30
  },
  scraper: {
    maxBrandsToProcess: parseInt(process.env.MAX_BRANDS_TO_PROCESS) || 0, // 0 = all brands
    skipFailedBrands: process.env.SKIP_FAILED_BRANDS === 'true',
    saveProgressFile: process.env.SAVE_PROGRESS_FILE !== 'false'
  }
};

// Initialize notification service
const notificationService = new NotificationService();
if (config.email.enabled && config.email.smtp.auth.user && config.email.fromEmail) {
  notificationService.initialize(config.email);
  console.log('📧 Email notifications enabled');
} else {
  console.log('📧 Email notifications disabled');
}

// Initialize failure logger
const failureLogger = new FailureLogger();
console.log(`📝 Failure logging enabled to Directus (Run ID: ${failureLogger.runId})`);

// Progress tracking
let progressState = {
  processedBrands: new Set(),
  failedBrands: new Set(),
  totalCarsScraped: 0,
  startTime: new Date(),
  lastSavedAt: new Date(),
  failureDetails: {} // Track detailed failure information
};

const PROGRESS_FILE = path.join(__dirname, 'scraper_progress.json');
const FAILURES_FILE = path.join(__dirname, 'scraper_failures.json');

// Load progress if exists
function loadProgress() {
  try {
    if (fs.existsSync(PROGRESS_FILE)) {
      const data = JSON.parse(fs.readFileSync(PROGRESS_FILE, 'utf8'));
      progressState.processedBrands = new Set(data.processedBrands || []);
      progressState.failedBrands = new Set(data.failedBrands || []);
      progressState.totalCarsScraped = data.totalCarsScraped || 0;
      progressState.startTime = new Date(data.startTime || Date.now());
      progressState.failureDetails = data.failureDetails || {};
      console.log(`📊 Loaded progress: ${progressState.processedBrands.size} brands processed, ${progressState.failedBrands.size} failed`);
    }
    
    // Load existing failure details if file exists
    if (fs.existsSync(FAILURES_FILE)) {
      const failureData = JSON.parse(fs.readFileSync(FAILURES_FILE, 'utf8'));
      // Merge existing failure details with loaded ones
      progressState.failureDetails = { ...failureData, ...progressState.failureDetails };
    }
  } catch (error) {
    console.warn('⚠️ Could not load progress file:', error.message);
  }
}

// Save progress
function saveProgress() {
  if (!config.scraper.saveProgressFile) return;
  
  try {
    const data = {
      processedBrands: Array.from(progressState.processedBrands),
      failedBrands: Array.from(progressState.failedBrands),
      totalCarsScraped: progressState.totalCarsScraped,
      startTime: progressState.startTime.toISOString(),
      lastSavedAt: new Date().toISOString(),
      failureDetails: progressState.failureDetails
    };
    fs.writeFileSync(PROGRESS_FILE, JSON.stringify(data, null, 2));
    progressState.lastSavedAt = new Date();
  } catch (error) {
    console.error('❌ Could not save progress:', error.message);
  }
}

// Save detailed failure information
function saveFailureDetails() {
  try {
    const failureData = {
      lastUpdated: new Date().toISOString(),
      runId: process.env.SCRAPER_RUN_ID || `run_${Date.now()}`,
      totalFailures: Object.keys(progressState.failureDetails).length,
      failures: progressState.failureDetails
    };
    
    fs.writeFileSync(FAILURES_FILE, JSON.stringify(failureData, null, 2));
    console.log(`📝 Saved failure details to ${FAILURES_FILE}`);
  } catch (error) {
    console.error('❌ Could not save failure details:', error.message);
  }
}

// Add failure detail
function addFailureDetail(brandName, error, context = {}) {
  const timestamp = new Date().toISOString();
  
  if (!progressState.failureDetails[brandName]) {
    progressState.failureDetails[brandName] = {
      brand: brandName,
      firstFailure: timestamp,
      attempts: [],
      totalAttempts: 0,
      finalError: null
    };
  }
  
  const failureDetail = {
    timestamp,
    error: error.message || String(error),
    stack: error.stack,
    context: {
      ...context,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      nodeVersion: process.version,
      platform: process.platform
    }
  };
  
  progressState.failureDetails[brandName].attempts.push(failureDetail);
  progressState.failureDetails[brandName].totalAttempts++;
  progressState.failureDetails[brandName].lastFailure = timestamp;
  progressState.failureDetails[brandName].finalError = failureDetail;
  
  console.log(`📝 Recorded failure for ${brandName}: ${error.message}`);
}

// Auto-save progress every 5 minutes
setInterval(saveProgress, 5 * 60 * 1000);

// 全メーカー定義（主要メーカー例）
const brands = [
  { name: "レクサス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LE" },
  { name: "トヨタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TO" },
  { name: "日産", url: "https://www.carsensor.net/usedcar/search.php?BRDC=NI" },
  { name: "ホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HO" },
  { name: "マツダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MA" },
  { name: "スバル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SB" },
  { name: "スズキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SZ" },
  { name: "三菱", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MI" },
  { name: "ダイハツ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DA" },
  { name: "いすゞ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=IS" },
  { name: "光岡自動車", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MT" },
  { name: "トミーカイラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TM" },
  { name: "日野", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HI" },
  { name: "UDトラックス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UD" },
  { name: "三菱ふそう", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FU" },
  { name: "国産車その他", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ZJ" },
  { name: "メルセデスベンツ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ME" },
  { name: "メルセデスAMG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AM" },
  { name: "メルセデス・マイバッハ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MM" },
  { name: "AMG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AG" },
  { name: "マイバッハ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MY" },
  { name: "スマート", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MC" },
  { name: "BMW", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BM" },
  { name: "BMWアルピナ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AL" },
  { name: "アウディ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AD" },
  { name: "フォルクスワーゲン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VW" },
  { name: "オペル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=OP" },
  { name: "ポルシェ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PO" },
  { name: "ルーフ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RU" },
  { name: "ブラバス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BR" },
  { name: "イエス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=YE" },
  { name: "カールソン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CL" },
  { name: "アルテガ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AA" },
  { name: "バーストナー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BN" },
  { name: "ミニ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MN" },
  { name: "キャデラック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CA" },
  { name: "シボレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CH" },
  { name: "ビュイック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BU" },
  { name: "ポンテアック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PN" },
  { name: "サターン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ST" },
  { name: "ハマー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HM" },
  { name: "GMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=GC" },
  { name: "フォード", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FO" },
  { name: "リンカーン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LI" },
  { name: "マーキュリー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MK" },
  { name: "サリーン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SL" },
  { name: "クライスラー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CR" },
  { name: "ダッジ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DG" },
  { name: "プリムス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PR" },
  { name: "AMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AC" },
  { name: "AMCジープ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AJ" },
  { name: "ジープ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=JE" },
  { name: "オールズモビル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=OL" },
  { name: "ウィネベーゴ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=WN" },
  { name: "DMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DC" },
  { name: "テスラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TS" },
  { name: "米国レクサス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UL" },
  { name: "米国インフィニティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UN" },
  { name: "米国アキュラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UA" },
  { name: "米国トヨタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UT" },
  { name: "米国サイオン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SI" },
  { name: "米国日産", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UN" },
  { name: "米国ホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UH" },
  { name: "米国マツダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UM" },
  { name: "米国スバル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UR" },
  { name: "米国スズキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=US" },
  { name: "米国三菱", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UB" },
  { name: "カナダホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CD" },
  { name: "ロールスロイス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RR" },
  { name: "ベントレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BE" },
  { name: "ジャガー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=JA" },
  { name: "デイムラー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DI" },
  { name: "ランドローバー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LR" },
  { name: "アストンマーティン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AS" },
  { name: "ロータス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RO" },
  { name: "ロンドンタクシー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LT" },
  { name: "マクラーレン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ML" },
  { name: "MG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MG" },
  { name: "ローバー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RV" },
  { name: "オースチン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AU" },
  { name: "モーリス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MR" },
  { name: "BL", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BL" },
  { name: "モーク", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MQ" },
  { name: "マーコス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MF" },
  { name: "バンデンプラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VP" },
  { name: "ライレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RI" },
  { name: "ケータハム", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CT" },
  { name: "ウエストフィールド", url: "https://www.carsensor.net/usedcar/search.php?BRDC=WE" },
  { name: "モーガン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MO" },
  { name: "パンサー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PT" },
  { name: "トライアンフ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TR" },
  { name: "ヒーレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HE" },
  { name: "ジネッタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=GI" },
  { name: "ボルボ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VO" },
  { name: "サーブ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SA" },
  { name: "ケーニッグゼグ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KO" },
  { name: "スカニア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SN" },
  { name: "プジョー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PE" },
  { name: "ルノー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RE" },
  { name: "シトロエン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CI" },
  { name: "DSオートモビル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DS" },
  { name: "アルピーヌ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AN" },
  { name: "フィアット", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FI" },
  { name: "アルファ　ロメオ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AF" },
  { name: "フェラーリ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FE" },
  { name: "ランボルギーニ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LG" },
  { name: "マセラティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MS" },
  { name: "ランチア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LA" },
  { name: "アウトビアンキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AT" },
  { name: "アバルト", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AB" },
  { name: "イノチェンティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=IN" },
  { name: "デトマソ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DE" },
  { name: "KTM", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KT" },
  { name: "セアト", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SE" },
  { name: "フータン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HA" },
  { name: "ドンカーブート", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DV" },
  { name: "ダチア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DB" },
  { name: "アドリア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AE" },
  { name: "ラーダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LD" },
  { name: "ワズ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UZ" },
  { name: "ホールデン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HL" },
  { name: "BYD", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BY" },
  { name: "ヒョンデ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HD" },
  { name: "起亜", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KI" },
  { name: "バーキン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BI" },
  { name: "輸入車その他", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ZZ" }
].map(brand => {
  const urlObj = new URL(brand.url);
  urlObj.searchParams.set('YMAX', '2005'); // ここで条件を指定
  return { ...brand, url: urlObj.toString() };
});

// Price range calculation functions for filtering search results
function getPriceRange100(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 2000) return '2000万円~';
  const min = Math.floor((val - 1) / 100) * 100 + 1;
  const max = Math.ceil(val / 100) * 100;
  return `${min}~${max}万円`;
}

function getPriceRange500(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 10000) return '1億1円~';
  if (val > 9500) return '9501万円~1億円';
  const min = Math.floor((val - 1) / 500) * 500 + 1;
  const max = Math.ceil(val / 500) * 500;
  if (max === 10000) return '9501万円~1億円';
  return `${min}~${max}万円`;
}

function getPriceRange1000(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 20000) return '2億1円~';
  if (val > 19000) return '1億9001~2億円';
  if (val > 18000) return '1億8001~1億9000万円';
  const min = Math.floor((val - 1) / 1000) * 1000 + 1;
  const max = Math.ceil(val / 1000) * 1000;
  if (max === 20000) return '1億9001~2億円';
  if (max === 19000) return '1億8001~1億9000万円';
  return `${min}~${max}万円`;
}

// Enhanced getDetails function with retry logic
async function getDetailsWithRetry(context, url) {
  const retryConfig = {
    maxAttempts: config.retry.detailMaxAttempts,
    baseDelayMs: config.retry.detailBaseDelayMs,
    maxDelayMs: 15000,
    backoffMultiplier: config.retry.backoffMultiplier,
    jitterMs: 200
  };

  return await RetryService.withRetry(async () => {
    const detailPage = await context.newPage();
    
    try {
      await detailPage.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });

      // 画像ギャラリーやimgが現れるまで待つ
      await detailPage.waitForFunction(() => {
        return (
          document.querySelector('a[data-photo]') ||
          Array.from(document.querySelectorAll('img')).some(img =>
            (img.getAttribute('data-src') && !img.getAttribute('data-src').includes('animation_M.gif')) ||
            (img.getAttribute('src') && !img.getAttribute('src').includes('animation_M.gif'))
          )
        );
      }, { timeout: 10000 });

      const data = await detailPage.evaluate(() => {
        // Extract image URLs and vehicle specification data
        const imageUrls = new Set();
        document.querySelectorAll('a[data-photo]').forEach(a => {
          const url = a.getAttribute('data-photo');
          if (url && !url.includes('animation_M.gif')) imageUrls.add(url);
        });
        document.querySelectorAll('img').forEach(img => {
          const dataSrc = img.getAttribute('data-src');
          if (dataSrc && !dataSrc.includes('animation_M.gif')) imageUrls.add(dataSrc);
          const src = img.getAttribute('src');
          if (src && !src.includes('animation_M.gif')) imageUrls.add(src);
        });
        const images = Array.from(imageUrls).slice(0, 10);

        const heading = document.getElementById('sec-kihon');
        if (!heading) return { images };
        const section = heading.closest('section');
        if (!section) return { images };
        const table = section.querySelector('div.defaultTable table.defaultTable__table');
        if (!table) return { images };
        const rows = Array.from(table.querySelectorAll('tbody > tr'));

        const td0 = rows[0]?.querySelectorAll('td.defaultTable__description');
        let driving_system = td0?.[1]?.textContent.trim() || '指定なし';
        if (driving_system === '不明') driving_system = '指定なし';

        const tdHandle = rows[1]?.querySelectorAll('td.defaultTable__description');
        let handle = tdHandle?.[1]?.textContent.trim() || '指定なし';
        if (handle === '不明') handle = '指定なし';

        const tdEngine = rows[4]?.querySelectorAll('td.defaultTable__description');
        let engine_type = tdEngine?.[0]?.textContent.trim() || 'その他';
        if (engine_type === '不明') engine_type = 'その他';

        const cats = Array.from(document.querySelectorAll('div.equipmentList__category'));
        const getEquip = idx => {
          if (!cats[idx]) return 'なし';
          const items = Array.from(cats[idx].querySelectorAll('li.equipmentList__item--active'));
          return items.map(el => el.textContent.trim()).join('\n') || 'なし';
        };

        // Extract model text from title - only the word between first space and second space (or line end)
        // EXAMPLE: <h1 class="title1">トヨタ&nbsp;ランドクルーザー100<span>4.7 VXリミテッド 4WD...</span></h1>
        let modelText = '指定なし';
        try {
          const titleElement = document.querySelector('h1.title1, h1[class*="title"], main h1, section h1');
          if (titleElement) {
            // Extract only the direct text nodes, excluding span content
            let titleText = '';
            const textNodes = [];
            for (const node of titleElement.childNodes) {
              if (node.nodeType === Node.TEXT_NODE) {
                const nodeText = node.textContent;
                textNodes.push(nodeText);
                titleText += nodeText;
              }
            }
            
            console.log('individual text nodes:', textNodes);
            console.log('combined text nodes:', titleText);
            console.log('full element text (with span):', titleElement.textContent);
            
            // Handle different types of spaces and slashes: regular space, &nbsp;, full-width space, etc.
            titleText = titleText.replace(/&nbsp;/g, ' ').replace(/\u00A0/g, ' ').replace(/\u3000/g, ' ').replace(/\s*\/\s*/g, ' ').trim();
            
            // Split by various space characters
            const spaceRegex = /[\s\u00A0\u3000]+/; // regular space, &nbsp;, full-width space
            const parts = titleText.split(spaceRegex);
            
            if (parts.length >= 2) {
              // Take only the second part (first part after maker name)
              // This gives us just the model name like "ランドクルーザー100" without the span subtitle
              modelText = parts[1].trim();
              
              // Simple cleanup: remove leading slashes and "その他" patterns
              modelText = modelText.replace(/^\/+/, '').replace(/^その他\s*/, '').trim();
              
              // Remove content in parentheses if present
              modelText = modelText.replace(/（[^）]*）/g, '').trim();
              
              // Validation: Check for suspicious patterns that might indicate span contamination
              // Look for patterns like "model4.2" or "model123abc" that suggest merged content
              const suspiciousPattern = /[0-9]+\.[0-9]/; // decimal numbers that might be from engine displacement
              if (suspiciousPattern.test(modelText)) {
                console.warn('Suspicious model text detected (possible span contamination):', modelText);
                // Try to clean it by removing anything after a decimal number
                modelText = modelText.replace(/[0-9]+\.[0-9].*$/, '').trim();
                console.log('Cleaned model text:', modelText);
              }
              
              // Fallback to default if extraction failed or empty
              if (!modelText || modelText === '不明') {
                modelText = '指定なし';
              }
            }
            console.log('final modelText:', modelText);
          }
        } catch (e) {
          console.warn('Model text extraction error:', e.message);
          modelText = '指定なし';
        }

        return {
          images: Array.from(imageUrls),
          driving_system,
          engine_type,
          handle,
          safeequipment: getEquip(0),
          comfortequipment: getEquip(1),
          interiaequipment: getEquip(2),
          exteriaequipment: getEquip(3),
          modelText
        };
      });

      if (data) {
        if (!data.images) data.images = [];
        for (const k of ['driving_system', 'engine_type', 'handle']) {
          if (!data[k]) data[k] = '';
        }
        return data;
      }
      
      return { images: [] };
    } finally {
      await detailPage.close();
    }
  }, retryConfig, {
    shouldRetry: (error) => RetryService.isRetryableError(error),
    onRetry: async (error, attempt) => {
      console.warn(`  🔄 Detail page retry ${attempt}/${retryConfig.maxAttempts} for ${url}: ${error.message}`);
      
      // Log detail page failure
      await failureLogger.logFailure({
        brandName: 'Detail Page',
        brandUrl: url,
        error,
        errorType: 'detail_failure',
        attemptNumber: attempt,
        maxAttempts: retryConfig.maxAttempts,
        context: { pageUrl: url, retryType: 'detail_page' }
      });
    }
  });
}

// Enhanced brand processing with retry logic
async function processBrandWithRetry(context, page, brand, allCars, outputManager) {
  const brandName = brand.name;
  const startUrl = brand.url;
  
  console.log(`🚀 Processing brand: ${brandName}`);
  
  const retryConfig = {
    maxAttempts: config.retry.brandMaxAttempts,
    baseDelayMs: config.retry.brandBaseDelayMs,
    maxDelayMs: 60000,
    backoffMultiplier: config.retry.backoffMultiplier,
    jitterMs: 1000
  };

  return await RetryService.withRetry(async () => {
    let pageCount = 1;
    let brandCars = [];
    
    await page.goto(startUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });

    // Check for zero results
    const zeroHit = await page.$('div.zeroHit');
    if (zeroHit) {
      console.log(`🔎 Brand "${brandName}" has zero results, skipping`);
      return { success: true, cars: [], skipped: true };
    }

    while (true) {
      const pageRetryConfig = {
        maxAttempts: config.retry.pageMaxAttempts,
        baseDelayMs: config.retry.pageBaseDelayMs,
        maxDelayMs: 30000,
        backoffMultiplier: config.retry.backoffMultiplier,
        jitterMs: 500
      };

      // Process current page with retry
      const pageCars = await RetryService.withRetry(async () => {
        await page.waitForSelector(
          '#carList > div > div.js-mainCassette > div.js_listTableCassette > div.cassetteMain',
          { timeout: 60000 }
        );

        // Extract car data
        const cars = await page.evaluate(() => {
          const get = (container, sel) =>
            container.querySelector(sel)?.textContent.trim().replace(/\u00A0/g, ' ') || null;
          const containers = document.querySelectorAll(
            '#carList > div > div.js-mainCassette > div.js_listTableCassette > div.cassetteMain'
          );

          return Array.from(containers).map(container => {
            const rawPrice = get(container, 'div.cassetteMain__priceInfo span.totalPrice__mainPriceNum');
            const priceStr = rawPrice === '応談' ? '0' : rawPrice;

            const y1 = get(container, 'div.cassetteMain__specInfo > dl > div:nth-child(1) > dd > span.specList__emphasisData') || '';
            const y2 = get(container, 'div.cassetteMain__specInfo > dl > div:nth-child(1) > dd > span.specList__jpYear') || '';
            const rawYear = y1 + y2;
            let modelYear = null;
            if (rawYear) {
              const m = rawYear.match(/^(\d{4})/);
              modelYear = m ? `${m[1]}/1/1` : rawYear;
            }

            return {
              car_name: get(container, 'h3 > a'),
              detail: get(container, 'p.cassetteMain__subText'),
              displacement: get(container, 'div:nth-child(7) > dd')?.replace(/CC/gi, '').trim(),
              fix_history: get(container, 'div:nth-child(4) > dd'),
              image: '',
              insurance: get(container, 'div:nth-child(5) > dd'),
              maintenance: get(container, 'div:nth-child(6) > dd'),
              maker: get(container, 'div.cassetteMain__carInfoContainer > p'),
              mileage: parseFloat(get(container, 'div:nth-child(2) > dd > span') || '0') * 10000,
              mission: get(container, 'div:nth-child(8) > dd'),
              Mission_type: /MT/.test(get(container, 'div:nth-child(8) > dd')) ? 'MT' :
                /AT|CVT/.test(get(container, 'div:nth-child(8) > dd')) ? 'AT/CVT' : '指定なし',
              model: '指定なし',
              model_year: modelYear,
              price: priceStr,
              price_range: (rawPrice === '応談' || priceStr === '0') ? '応談' : null,
              price_range_500: (rawPrice === '応談' || priceStr === '0') ? '応談' : null,
              price_range_1000: (rawPrice === '応談' || priceStr === '0') ? '応談' : null,
              region: '日本（カーセンサー）',
              source: 'カーセンサー',
              sourceURL: container.querySelector('h3 > a')?.href || null,
              vehicle_inspection: get(container, 'div:nth-child(3) > dd'),
              driving_system: '指定なし',
              engine_type: '指定なし',
              handle: '指定なし',
              safe_equipment: 'なし',
              comfort_equipment: 'なし',
              interia_equipment: 'なし',
              exteria_equipment: 'なし'
            };
          });
        });

        return cars;
      }, pageRetryConfig, {
        shouldRetry: (error) => RetryService.isRetryableError(error),
        onRetry: async (error, attempt) => {
          console.warn(`  🔄 Page retry ${attempt}/${pageRetryConfig.maxAttempts} for ${brandName} page ${pageCount}: ${error.message}`);
          
          // Log page failure
          await failureLogger.logFailure({
            brandName,
            brandUrl: startUrl,
            error,
            errorType: 'page_failure',
            attemptNumber: attempt,
            maxAttempts: pageRetryConfig.maxAttempts,
            context: { pageNumber: pageCount, retryType: 'page_processing' }
          });
        }
      });

      // Calculate price ranges
      for (const car of pageCars) {
        const priceVal = parseFloat(car.price);
        if (car.price_range !== '応談') {
          car.price_range = getPriceRange100(priceVal);
        }
        if (car.price_range_500 !== '応談') {
          car.price_range_500 = getPriceRange500(priceVal);
        }
        if (car.price_range_1000 !== '応談') {
          car.price_range_1000 = getPriceRange1000(priceVal);
        }
      }

      // Get details for each car with retry
      for (const car of pageCars) {
        if (!car.sourceURL) continue;
        
        try {
          const details = await getDetailsWithRetry(context, car.sourceURL);
          if (details) {
            car.driving_system = details.driving_system;
            car.engine_type = details.engine_type;
            car.handle = details.handle;
            car.model = details.modelText;
            car.safe_equipment = details.safeequipment;
            car.comfort_equipment = details.comfortequipment;
            car.interia_equipment = details.interiaequipment;
            car.exteria_equipment = details.exteriaequipment;
            car.image = (details.images || []).slice(0, 10).join(',');
          }
        } catch (error) {
          console.error(`  ❌ Failed to get details for car in ${brandName}:`, error.message);
          // Continue with other cars even if one fails
        }
        
        await page.waitForTimeout(200 + Math.random() * 300);
      }

      console.log(`  📄 Processed page ${pageCount} for ${brandName}: ${pageCars.length} cars`);
      
      // Filter out blacklisted cars
      const filteredCars = [];
      let blacklistedCount = 0;
      
      for (const car of pageCars) {
        const blacklistResult = await blacklistManager.isBlacklisted(car);
        if (blacklistResult.isBlacklisted) {
          blacklistedCount++;
          console.log(`    🚫 Filtered out: ${car.car_name || 'Unknown'} (${blacklistResult.reason})`);
        } else {
          filteredCars.push(car);
        }
      }
      
      if (blacklistedCount > 0) {
        console.log(`  🛡️  Filtered ${blacklistedCount}/${pageCars.length} cars from blacklist`);
      }
      
      brandCars.push(...filteredCars);

      // Insert each processed page immediately into the database
      if (filteredCars.length > 0) {
        try {
          await outputManager.saveData(filteredCars, `${brandName}_page_${pageCount}`);
          console.log(`    💾 Immediately saved ${filteredCars.length} cars from page ${pageCount} to database`);
        } catch (error) {
          console.error(`    ❌ Failed to save page ${pageCount} data for ${brandName}:`, error.message);
          // Continue processing other pages even if one page fails to save
        }
      } else if (pageCars.length > 0) {
        console.log(`    ⚠️  All ${pageCars.length} cars from page ${pageCount} were filtered by blacklist`);
      }

      // Check for next page
      const nextBtn = await page.$('#js-resultBar .pager__btn__next:not(.is-disabled)');
      if (!nextBtn) break;
      
      await Promise.all([
        nextBtn.click(),
        page.waitForLoadState('domcontentloaded')
      ]);
      pageCount++;
      await page.waitForTimeout(500 + Math.random() * 500);
    }

    return { success: true, cars: brandCars, skipped: false };
  }, retryConfig, {
    shouldRetry: (error) => {
      const isRetryable = RetryService.isRetryableError(error);
      if (!isRetryable && config.email.enabled) {
        // Send brand failure alert for non-retryable errors
        notificationService.sendAlert({
          type: 'brand_failure',
          brand: brandName,
          message: `Non-retryable error encountered for brand ${brandName}`,
          error: error.message
        }).catch(console.error);
      }
      return isRetryable;
    },
    onRetry: async (error, attempt) => {
      console.warn(`🔄 Brand retry ${attempt}/${retryConfig.maxAttempts} for ${brandName}: ${error.message}`);
      
      // Log brand failure
      await failureLogger.logFailure({
        brandName,
        brandUrl: startUrl,
        error,
        errorType: 'brand_failure',
        attemptNumber: attempt,
        maxAttempts: retryConfig.maxAttempts,
        context: { retryType: 'brand_processing' }
      });
      
      if (config.email.enabled) {
        notificationService.sendAlert({
          type: 'brand_failure',
          brand: brandName,
          message: `Brand processing attempt ${attempt} failed, will retry`,
          error: error.message,
          attemptNumber: attempt,
          totalAttempts: retryConfig.maxAttempts
        }).catch(console.error);
      }
    }
  });
}

// Main execution
(async () => {
  let browser;
  let context;
  let page;
  
  try {
    // Load progress
    loadProgress();
    
    // Initialize browser
    browser = await chromium.launch({ headless: true });
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });
    page = await context.newPage();

    // Initialize data output manager
    const outputManager = dataOutputManager;
    console.log(`📊 Output mode: ${outputManager.outputMode}`);

    // Prepare output directory
    const outputDir = path.resolve(__dirname, 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Filter brands to process
    let brandsToProcess = brands;
    
    // Skip already processed brands if configured
    if (config.scraper.skipFailedBrands) {
      brandsToProcess = brands.filter(brand => 
        !progressState.processedBrands.has(brand.name) && 
        !progressState.failedBrands.has(brand.name)
      );
      console.log(`📊 Skipping ${brands.length - brandsToProcess.length} already processed brands`);
    }

    // Limit brands if configured
    if (config.scraper.maxBrandsToProcess > 0) {
      brandsToProcess = brandsToProcess.slice(0, config.scraper.maxBrandsToProcess);
      console.log(`📊 Processing limited set: ${brandsToProcess.length} brands`);
    }

    const allCars = [];
    let processedCount = 0;
    let failedCount = 0;
    
    console.log(`🚀 Starting enhanced scraper for ${brandsToProcess.length} brands`);

    // Process each brand with enhanced retry logic
    for (const brand of brandsToProcess) {
      const brandName = brand.name;
      
      try {
        const result = await processBrandWithRetry(context, page, brand, allCars, outputManager);
        
        if (result.success) {
          if (result.skipped) {
            console.log(`⏭️  Skipped ${brandName} (zero results)`);
            progressState.processedBrands.add(brandName);
          } else {
            const brandCars = result.cars;
            allCars.push(...brandCars);
            progressState.totalCarsScraped += brandCars.length;
            progressState.processedBrands.add(brandName);
            
            // Note: Each page has already been saved to database immediately during processing
            console.log(`✅ Completed ${brandName}: ${brandCars.length} cars (Total: ${progressState.totalCarsScraped})`);
          }
          processedCount++;
        }
        
      } catch (error) {
        failedCount++;
        progressState.failedBrands.add(brandName);
        
        console.error(`❌ Brand ${brandName} failed after all retries:`, error.message);
        
        // Log final brand failure
        await failureLogger.logFailure({
          brandName,
          brandUrl: brand.url,
          error,
          errorType: 'total_failure',
          attemptNumber: config.retry.brandMaxAttempts,
          maxAttempts: config.retry.brandMaxAttempts,
          context: { retryType: 'final_failure', isFinalAttempt: true }
        });
        
        // Send total failure alert
        if (config.email.enabled) {
          await notificationService.sendAlert({
            type: 'total_failure',
            brand: brandName,
            message: `Brand ${brandName} failed after ${config.retry.brandMaxAttempts} retry attempts`,
            error: error.message,
            attemptNumber: config.retry.brandMaxAttempts,
            totalAttempts: config.retry.brandMaxAttempts
          }).catch(console.error);
        }
      }
      
      // Save progress periodically
      if (processedCount % 5 === 0) {
        saveProgress();
      }
      
      // Brief pause between brands
      await page.waitForTimeout(1000 + Math.random() * 2000);
    }

    // Final data processing
    console.log(`📊 Processing complete. Total cars scraped: ${allCars.length}`);
    
    // Limit images to 10 per car
    allCars.forEach(car => {
      if (car.image) {
        car.image = car.image.split(',').filter(Boolean).slice(0, 10).join(',');
      }
    });

    // CSV export
    const header = [
      'car_name', 'detail', 'displacement', 'driving_system', 'engine_type', 'fix_history', 'handle',
      'image', 'insurance', 'maintenance', 'maker', 'mileage', 'mission', 'Mission_type', 'model', 'model_year',
      'price', 'price_range', 'price_range_500', 'price_range_1000',
      'region', 'source', 'sourceURL', 'vehicle_inspection',
      'safe_equipment', 'comfort_equipment', 'interia_equipment', 'exteria_equipment'
    ];

    const csvBody = allCars.map(c =>
      header.map(h => {
        const v = c[h] ?? '';
        return /[",\n]/.test(v) ? `"${v.replace(/"/g, '""')}"` : v;
      }).join(',')
    ).join('\n');

    // Generate timestamp
    const jst = new Date(Date.now() + 9 * 3600 * 1000);
    const pad = n => n.toString().padStart(2, '0');
    const Y = jst.getUTCFullYear();
    const M = pad(jst.getUTCMonth() + 1);
    const D = pad(jst.getUTCDate());
    const h = pad(jst.getUTCHours());
    const m = pad(jst.getUTCMinutes());
    const s = pad(jst.getUTCSeconds());
    const dateString = `${Y}-${M}-${D}-${h}-${m}-${s}`;
    
    const filename = path.join(outputDir, `enhanced_scraper_${dateString}.csv`);
    fs.writeFileSync(filename, '\uFEFF' + header.join(',') + '\n' + csvBody);
    console.log(`📁 CSV written to: ${filename}`);

    // Final progress save
    saveProgress();
    
    // Success summary
    const duration = Math.round((Date.now() - progressState.startTime.getTime()) / 1000 / 60);
    const summary = `Enhanced scraping completed successfully!\n` +
      `• Processed: ${processedCount} brands\n` +
      `• Failed: ${failedCount} brands\n` +
      `• Total cars: ${allCars.length}\n` +
      `• Duration: ${duration} minutes\n` +
      `• CSV: ${filename}`;
      
    console.log(`🎉 ${summary}`);
    
    // Send success summary email
    if (config.email.enabled) {
      await notificationService.sendAlert({
        type: 'success_summary',
        message: summary
      }).catch(console.error);
    }

  } catch (error) {
    console.error('🔥 Critical error in enhanced scraper:', error);
    
    // Send critical error alert
    if (config.email.enabled) {
      await notificationService.sendAlert({
        type: 'critical_error',
        runId: process.env.SCRAPER_RUN_ID || 'unknown',
        message: 'Critical error in enhanced scraper execution',
        error: error.message
      }).catch(console.error);
    }
    
    throw error;
  } finally {
    try {
      if (context) await context.close();
      if (browser) await browser.close();
      console.log('🧹 Browser cleaned up');
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError);
    }
  }
})().catch(error => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});