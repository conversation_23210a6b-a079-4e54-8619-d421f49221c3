# Used Car Sales Scraper with Database Support

A comprehensive web scraping solution for used car data that supports both CSV export and database storage using CockroachDB (PostgreSQL-compatible).

## Features

- 🚗 **Multi-brand car scraping** from CarSensor
- 📊 **Dual output modes**: CSV files and/or database storage
- 🐘 **CockroachDB integration** with Docker setup
- 🔄 **Batch upsert functionality** with conflict resolution
- 📈 **Data deduplication** based on source URLs
- 🚫 **Blacklist filtering** to exclude unwanted vehicles/models
- 🛠️ **Migration and seeding tools**
- ⚡ **Connection pooling** for optimal performance
- 🧪 **Health checks** and error handling

## Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit configuration as needed
nano .env
```

### 2. Start CockroachDB

```bash
# Start the database container
docker-compose up -d

# Verify the database is running
docker-compose ps
```

### 3. Initialize Database

```bash
# Run database migrations
node scripts/migrate.js migrate

# Optional: Seed with existing CSV data
node scripts/seed.js seed
```

### 4. Run Scraper

```bash
# Run latest scraper with database and CSV support
node carsensor.js

# Or use npm scripts
npm start                    # Run with default settings
npm run scrape:db           # Database output only
npm run scrape:csv          # CSV output only
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `localhost` | Database host |
| `DB_PORT` | `26257` | Database port |
| `DB_NAME` | `usedcarsales` | Database name |
| `DB_USER` | `root` | Database user |
| `DB_PASSWORD` | `` | Database password |
| `DB_SSL` | `false` | Enable SSL connection |
| `OUTPUT_MODE` | `both` | Output mode: `csv`, `database`, or `both` |
| `BATCH_SIZE` | `100` | Batch size for database operations |
| `CSV_OUTPUT_DIR` | `./output` | Directory for CSV files |

### Output Modes

- **`csv`**: Save data only to CSV files
- **`database`**: Save data only to database
- **`both`**: Save to both CSV and database (recommended)

## Database Schema

The `used_cars` table includes:

- **Basic Info**: car_name, maker, model, model_year
- **Technical**: displacement, engine_type, mission, driving_system
- **Condition**: mileage, fix_history, vehicle_inspection
- **Pricing**: price, price_range
- **Location**: region, source, source_url
- **Equipment**: JSON arrays for safety, comfort, interior, exterior features
- **Metadata**: created_at, updated_at, scraped_at

## Blacklist Feature

The blacklist feature allows you to exclude specific vehicles, models, or manufacturers from being scraped and stored. This is useful for filtering out unwanted cars based on various criteria.

### Blacklist Types

1. **Maker**: Block entire car manufacturers (e.g., "メルセデスベンツ")
2. **Model**: Block specific car models (e.g., "C-Class", "E-Class")
3. **Car Name Pattern**: Block cars matching regex patterns in the car_name field
4. **Car Name Exact**: Block cars with exact car_name matches

### Configuration

Blacklist behavior can be configured via environment variables:

```bash
# Enable/disable blacklist filtering
BLACKLIST_ENABLED=true

# Cache expiry in milliseconds (default: 5 minutes)
BLACKLIST_CACHE_EXPIRY=300000

# Fail mode: continue scraping if blacklist check fails
BLACKLIST_FAIL_OPEN=true
```

### How It Works

1. **During Scraping**: Each car is checked against the blacklist before being saved
2. **Caching**: Blacklist entries are cached for performance (configurable expiry)
3. **Pattern Matching**: Supports regex patterns for flexible filtering
4. **Fail-Safe**: Configurable behavior when blacklist checks fail

## Scripts

### Migration Scripts

```bash
# Run all migrations
node scripts/migrate.js migrate

# Reset database (drop and recreate)
node scripts/migrate.js reset
```

### Seeding Scripts

```bash
# Import all CSV files to database
node scripts/seed.js seed

# Clear all database data
node scripts/seed.js clear

# Show database statistics
node scripts/seed.js stats
```

### Blacklist Management

```bash
# Add blacklist entries
node scripts/blacklist.js add maker "メルセデスベンツ" "Too expensive"
node scripts/blacklist.js add model "C-Class" "Not interested"
node scripts/blacklist.js add car_name_pattern "事故車" "Accident vehicles"

# List blacklist entries
node scripts/blacklist.js list

# Test if a car would be blacklisted
node scripts/blacklist.js test "BMW 3シリーズ 320i" "BMW" "3シリーズ"

# Remove blacklist entries
node scripts/blacklist.js remove maker "メルセデスベンツ"

# Clear all blacklist entries
node scripts/blacklist.js clear
```

### Testing

```bash
# Run all tests
node scripts/test.js

# Run specific test file
node tests/blacklist-manager.test.js
```

## API Usage

### Data Output Manager

```javascript
const dataOutputManager = require('./lib/data-output-manager');

// Save car data (respects OUTPUT_MODE setting)
await dataOutputManager.saveData(cars, brandName);

// Change output mode programmatically
dataOutputManager.setOutputMode('database');
```

### Database Connection

```javascript
const dbConnection = require('./database/connection');

// Execute query
const result = await dbConnection.query('SELECT * FROM used_cars LIMIT 10');

// Health check
const health = await dbConnection.healthCheck();
```

### Blacklist Manager

```javascript
const blacklistManager = require('./lib/blacklist-manager');

// Add blacklist entries
await blacklistManager.addEntry('maker', 'メルセデスベンツ', 'Too expensive');
await blacklistManager.addEntry('model', 'C-Class', 'Not interested');
await blacklistManager.addEntry('car_name_pattern', '事故車', 'Accident vehicles');

// Check if a car is blacklisted
const result = await blacklistManager.isBlacklisted({
    car_name: 'BMW 3シリーズ 320i',
    maker: 'BMW',
    model: '3シリーズ'
});

if (result.isBlacklisted) {
    console.log(`Car is blacklisted: ${result.reason}`);
}

// List all blacklist entries
const entries = await blacklistManager.listEntries();

// Remove blacklist entry
await blacklistManager.removeEntry('maker', 'メルセデスベンツ');
```

### Batch Upsert

```javascript
const batchUpsert = require('./database/batch-upsert');

// Upsert car data
const result = await batchUpsert.upsertCars(carDataArray);
console.log(`${result.inserted} inserted, ${result.updated} updated`);
```

## Database Management

### Access CockroachDB Admin UI

Open http://localhost:8080 in your browser to access the CockroachDB admin interface.

### Connect via CLI

```bash
# Connect to database
docker exec -it usedcar_cockroachdb ./cockroach sql --insecure

# Or use any PostgreSQL client
psql "postgresql://root@localhost:26257/usedcarsales?sslmode=disable"
```

### Backup and Restore

```bash
# Backup
docker exec usedcar_cockroachdb ./cockroach dump usedcarsales --insecure > backup.sql

# Restore
docker exec -i usedcar_cockroachdb ./cockroach sql --insecure < backup.sql
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check database health
curl http://localhost:8080/health?ready=1

# Check application health
node -e "require('./database/connection').healthCheck().then(console.log)"
```

### Performance Monitoring

- Monitor connection pool usage
- Check query performance in CockroachDB admin UI
- Review batch processing logs for optimization

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure Docker container is running: `docker-compose ps`
   - Check port availability: `netstat -an | grep 26257`
   - Verify environment variables in `.env`

2. **Migration Errors**
   - Check database permissions
   - Ensure database exists: `docker exec usedcar_cockroachdb ./cockroach sql --insecure -e "SHOW DATABASES;"`

3. **Scraping Issues**
   - Check network connectivity
   - Verify target website structure hasn't changed
   - Review browser automation settings

### Logs and Debugging

- Database logs: `docker-compose logs cockroachdb`
- Application logs: Check console output for detailed error messages
- Enable debug mode by setting `NODE_ENV=development`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational and research purposes only. Please respect the terms of service of the websites being scraped.
