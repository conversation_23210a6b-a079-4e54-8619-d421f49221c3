{"name": "usedcarsales-scraper", "version": "1.0.0", "description": "Used car sales scraper with database support", "main": "carsensor.js", "scripts": {"start": "node carsensor.js", "scrape:csv": "OUTPUT_MODE=csv node carsensor.js", "scrape:db": "OUTPUT_MODE=database node carsensor.js", "test": "node test-enhanced.js", "db:migrate": "node scripts/migrate.js migrate", "db:reset": "node scripts/migrate.js reset", "db:seed": "node scripts/seed.js seed", "db:clear": "node scripts/seed.js clear", "db:stats": "node scripts/seed.js stats", "health": "node scripts/health-check.js", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f cockroachdb"}, "dependencies": {"csv-parse": "^5.6.0", "dotenv": "^17.2.1", "fast-csv": "^5.0.2", "pg": "^8.16.3", "playwright": "^1.51.1"}, "optionalDependencies": {"nodemailer": "^7.0.5", "resend": "^6.0.1"}, "keywords": ["scraping", "cars", "database", "cockroachdb", "csv"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/nodemailer": "^6.4.17"}}