const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// 定数定義
const CONSTANTS = {
    URLS: {
        SEARCH_URL: 'https://suchen.mobile.de/fahrzeuge/search.html?con=USED&dam=false&isSearchRequest=true&ms=20100%3B3%3B%3B&ref=dsp&s=Car&vc=Car&lang=en'
    },
    SELECTORS: {
        CONSENT_MODAL: '#mde-consent-modal-dialog > div.sc-jCbFiK.fIuaZF > div.sc-cBYhjr.jMPPDq > div > span',
        CAR_CONTAINERS: 'div.K0qQI',
        CAR_NAME: 'div.urhVn > h2 > span.LBG5d',
        CAR_DETAIL: 'div.urhVn > h2 > span.Z_aNr',
        CAR_IMAGE: 'section > div > div > a > div.fEytW > div > div > img.Qj_9F',
        CAR_PRICE: 'section > div > div > a > div.K0qQI > div.Pn6b3 > div.QWkBK > div > span',
        NEXT_BUTTON_DISABLED: '#js-resultBar > div.resultBar__link > div > div.pager__btn > button.btnFunc.pager__btn__next.is-disabled'
    },
    TIMEOUTS: {
        PAGE_LOAD: 5000,
        WAIT_BETWEEN_ACTIONS: 5000
    },
    CSV_HEADERS: [
        'car_name', 'detail', 'displacement', 'driving_system', 'engine_type', 
        'fix_history', 'handle', 'image', 'insurance', 'maintenance', 'maker', 
        'mileage', 'mission', 'Mission_type', 'model', 'model_year', 'price', 
        'price_range', 'region', 'source', 'sourceURL', 'vehicle_inspection'
    ]
};

/**
 * メイン処理実行関数
 */
(async () => {
    let browser = null;
    
    try {
        // ブラウザの初期化
        browser = await initializeBrowser();
        const page = await createPage(browser);
        
        // mobile.deのページにアクセス
        await navigateToSearchPage(page);
        
        // 同意モーダルを処理
        await handleConsentModal(page);
        
        // 車両データを抽出してCSV保存
        await extractAndSaveCarData(page);
        
        console.log('処理が正常に完了しました');
        
    } catch (error) {
        console.error('エラーが発生しました:', error);
    } finally {
        // ブラウザのクリーンアップ
        if (browser) {
            await browser.close();
        }
    }
})();

/**
 * ブラウザの初期化
 * @returns {Promise<Browser>} ブラウザインスタンス
 */
async function initializeBrowser() {
    console.log('ブラウザを起動中...');
    return await chromium.launch({ headless: false });
}

/**
 * 新しいページを作成
 * @param {Browser} browser ブラウザインスタンス
 * @returns {Promise<Page>} ページインスタンス
 */
async function createPage(browser) {
    const context = await browser.newContext();
    return await context.newPage();
}

/**
 * 検索ページにアクセス
 * @param {Page} page ページインスタンス
 */
async function navigateToSearchPage(page) {
    console.log('mobile.deの検索ページにアクセス中...');
    await page.goto(CONSTANTS.URLS.SEARCH_URL);
    await page.waitForTimeout(CONSTANTS.TIMEOUTS.PAGE_LOAD);
}

/**
 * 同意モーダルダイアログを処理
 * @param {Page} page ページインスタンス
 */
async function handleConsentModal(page) {
    console.log('同意モーダルを処理中...');
    try {
        await page.locator(CONSTANTS.SELECTORS.CONSENT_MODAL).click();
        console.log('同意モーダルを正常にクリックしました');
    } catch (error) {
        console.warn('同意モーダルの処理に失敗しました:', error.message);
    }
}

/**
 * 車両データを抽出してCSVファイルに保存
 * @param {Page} page ページインスタンス
 */
async function extractAndSaveCarData(page) {
    let pageCount = 1;
    let shouldContinue = true;
    
    while (shouldContinue) {
        console.log(`ページ ${pageCount} のデータを抽出中...`);
        
        // ページの読み込み完了を待機
        await page.waitForTimeout(CONSTANTS.TIMEOUTS.WAIT_BETWEEN_ACTIONS);
        
        // 車両データを抽出
        const carData = await extractCarDataFromPage(page);
        console.log(`${carData.length} 件の車両データを抽出しました`);
        
        // CSVファイルに保存
        await saveDataToCSV(carData);
        
        // 次のページがあるかチェック（現在の実装では1ページのみ処理）
        shouldContinue = await checkForNextPage(page);
        
        if (shouldContinue) {
            pageCount++;
        }
    }
}

/**
 * ページから車両データを抽出
 * @param {Page} page ページインスタンス
 * @returns {Promise<Array>} 抽出された車両データの配列
 */
async function extractCarDataFromPage(page) {
    return await page.evaluate((selectors) => {
        const containers = document.querySelectorAll(selectors.CAR_CONTAINERS);
        const maker = "ポルシェ"; // 固定値として設定
        
        return Array.from(containers).map(container => {
            // 各車両の情報を抽出
            const carNameElement = container.querySelector(selectors.CAR_NAME);
            const detailElement = container.querySelector(selectors.CAR_DETAIL);
            const imageElement = container.querySelector(selectors.CAR_IMAGE);
            const priceElement = container.querySelector(selectors.CAR_PRICE);
            
            return {
                car_name: carNameElement ? carNameElement.textContent.trim() : null,
                detail: detailElement ? detailElement.textContent.trim() : null,
                displacement: null, // 一覧ページでは取得不可
                driving_system: "不明(一覧になし)",
                engine_type: "不明(一覧になし)",
                fix_history: null,
                handle: "不明(一覧になし)",
                image: imageElement ? imageElement.getAttribute('src') : null,
                insurance: null,
                maintenance: null,
                maker: maker,
                mileage: null,
                mission: null,
                Mission_type: "不明(一覧になし)",
                model: "不明(一覧になし)",
                model_year: null,
                price: priceElement ? priceElement.textContent.trim() : null,
                price_range: "おそらく不要",
                region: "不明(一覧になし)",
                source: "mobile.de",
                sourceURL: null,
                vehicle_inspection: null
            };
        });
    }, CONSTANTS.SELECTORS);
}

/**
 * データをCSVファイルに保存
 * @param {Array} carData 車両データの配列
 */
async function saveDataToCSV(carData) {
    try {
        // 出力ディレクトリの作成
        const outputDir = 'output';
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        // CSVデータの生成
        const csvData = convertDataToCSV(carData);
        
        // ファイル名の生成（タイムスタンプ付き）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = path.join(outputDir, `mobilede_output_${timestamp}.csv`);
        
        // BOM付きUTF-8でファイル保存（Excel対応）
        fs.writeFileSync(filename, '\ufeff' + csvData);
        
        console.log(`CSVファイルを保存しました: ${filename}`);
        
    } catch (error) {
        console.error('CSV保存中にエラーが発生しました:', error);
        throw error;
    }
}

/**
 * 車両データをCSV形式に変換
 * @param {Array} carData 車両データの配列
 * @returns {string} CSV形式の文字列
 */
function convertDataToCSV(carData) {
    // ヘッダー行の追加
    const headerRow = CONSTANTS.CSV_HEADERS.join(',');
    
    // データ行の変換
    const dataRows = carData.map(car => {
        const rowData = CONSTANTS.CSV_HEADERS.map(header => {
            let value = car[header];
            
            // null値を空文字に変換
            if (value === null || value === undefined) {
                value = '';
            }
            
            // カンマが含まれる場合はダブルクォートで囲む
            if (String(value).includes(',')) {
                return `"${value}"`;
            }
            
            return value;
        });
        
        return rowData.join(',');
    });
    
    // ヘッダーとデータを結合
    return [headerRow, ...dataRows].join('\n');
}

/**
 * 次のページが存在するかチェック
 * @param {Page} page ページインスタンス
 * @returns {Promise<boolean>} 次のページが存在する場合true
 */
async function checkForNextPage(page) {
    try {
        // 次ページボタンが無効化されているかチェック
        const nextButtonDisabled = await page.$(CONSTANTS.SELECTORS.NEXT_BUTTON_DISABLED);
        const hasNextPage = nextButtonDisabled === null;
        
        console.log(`次のページは${hasNextPage ? '存在します' : '存在しません'}`);
        
        // 現在の実装では常に1ページのみ処理
        return false;
        
    } catch (error) {
        console.error('次ページチェック中にエラーが発生しました:', error);
        return false;
    }
}

