class RetryService {
  static async withRetry(operation, config, options = {}) {
    let lastError;
    
    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // Check if we should retry this error
        if (options.shouldRetry && !options.shouldRetry(error)) {
          throw error;
        }
        
        // Don't wait after the last attempt
        if (attempt === config.maxAttempts) {
          break;
        }
        
        // Calculate delay with exponential backoff
        const baseDelay = config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1);
        const cappedDelay = Math.min(baseDelay, config.maxDelayMs);
        const jitter = config.jitterMs ? Math.random() * config.jitterMs : 0;
        const delay = cappedDelay + jitter;
        
        // Call retry callback
        if (options.onRetry) {
          options.onRetry(error, attempt);
        }
        
        console.warn(`Retry attempt ${attempt}/${config.maxAttempts} failed, waiting ${delay}ms:`, error.message);
        await this.delay(delay);
      }
    }
    
    throw lastError;
  }
  
  static async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Predefined retry configurations
  static get BRAND_RETRY_CONFIG() {
    return {
      maxAttempts: 3,
      baseDelayMs: 5000,     // 5 seconds
      maxDelayMs: 60000,     // 1 minute
      backoffMultiplier: 3,  // 5s, 15s, 45s
      jitterMs: 1000         // ±1s random jitter
    };
  }
  
  static get PAGE_RETRY_CONFIG() {
    return {
      maxAttempts: 3,
      baseDelayMs: 2000,     // 2 seconds
      maxDelayMs: 30000,     // 30 seconds
      backoffMultiplier: 3,  // 2s, 6s, 18s
      jitterMs: 500          // ±0.5s random jitter
    };
  }
  
  static get DETAIL_RETRY_CONFIG() {
    return {
      maxAttempts: 3,
      baseDelayMs: 1000,     // 1 second
      maxDelayMs: 15000,     // 15 seconds
      backoffMultiplier: 3,  // 1s, 3s, 9s
      jitterMs: 200          // ±0.2s random jitter
    };
  }
  
  // Error classification helpers
  static isRetryableError(error) {
    if (!error) return false;
    
    const message = error.message?.toLowerCase() || '';
    const code = error.code?.toLowerCase() || '';
    
    // Network errors that are typically retryable
    const retryableErrors = [
      'timeout',
      'network error',
      'connection refused',
      'connection reset',
      'enotfound',
      'econnreset',
      'econnrefused',
      'etimedout',
      'socket hang up',
      'temporary failure',
      'service unavailable',
      '502',
      '503',
      '504',
      'rate limit',
      'too many requests'
    ];
    
    return retryableErrors.some(pattern => 
      message.includes(pattern) || code.includes(pattern)
    );
  }
  
  static isCriticalError(error) {
    if (!error) return false;
    
    const message = error.message?.toLowerCase() || '';
    const code = error.code?.toLowerCase() || '';
    
    // Errors that indicate serious problems
    const criticalErrors = [
      'browser crashed',
      'context disposed',
      'page closed',
      'browser closed',
      'out of memory',
      'database connection failed',
      'authentication failed',
      'permission denied',
      'disk full',
      'no space left'
    ];
    
    return criticalErrors.some(pattern => 
      message.includes(pattern) || code.includes(pattern)
    );
  }
}

module.exports = { RetryService };