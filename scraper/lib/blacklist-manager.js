const dbConnection = require('../database/connection');

/**
 * Simplified Blacklist Manager
 * Always-on keyword-based filtering for car names
 * Managed through web UI (database records only)
 */
class BlacklistManager {
    constructor() {
        this.cache = new Set(); // Cache keywords for performance
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = 0;
    }

    /**
     * Get all active blacklist keywords from database
     */
    async getKeywords() {
        try {
            const query = `
                SELECT keyword 
                FROM blacklist 
                WHERE is_active = true AND keyword IS NOT NULL
                ORDER BY keyword
            `;
            const result = await dbConnection.query(query);
            return result.rows.map(row => row.keyword.toLowerCase());
        } catch (error) {
            console.error(`❌ Failed to get blacklist keywords: ${error.message}`);
            return []; // Return empty array on error
        }
    }



    /**
     * Check if a car should be blacklisted based on keywords in car_name
     */
    async isBlacklisted(carData) {
        try {
            // Ensure cache is fresh
            await this.refreshCacheIfNeeded();

            const { car_name } = carData;
            
            if (!car_name || typeof car_name !== 'string') {
                return { isBlacklisted: false };
            }

            const carNameLower = car_name.toLowerCase();
            
            // Check if car name contains any blacklisted keywords
            for (const keyword of this.cache) {
                if (carNameLower.includes(keyword)) {
                    return {
                        isBlacklisted: true,
                        reason: `Car name contains blacklisted keyword: "${keyword}"`,
                        keyword: keyword
                    };
                }
            }

            return { isBlacklisted: false };
        } catch (error) {
            console.error(`❌ Error checking blacklist: ${error.message}`);
            // Fail open - allow car through if blacklist check fails
            return {
                isBlacklisted: false,
                error: error.message,
                reason: 'Blacklist check failed (fail open)'
            };
        }
    }

    /**
     * Refresh cache if needed
     */
    async refreshCacheIfNeeded() {
        const now = Date.now();
        if (now - this.lastCacheUpdate > this.cacheExpiry) {
            await this.refreshCache();
        }
    }

    /**
     * Refresh the cache with current blacklist keywords
     */
    async refreshCache() {
        try {
            const keywords = await this.getKeywords();
            this.cache.clear();

            for (const keyword of keywords) {
                this.cache.add(keyword);
            }

            this.lastCacheUpdate = Date.now();
            console.log(`🔄 Blacklist cache refreshed: ${keywords.length} keywords loaded`);
        } catch (error) {
            console.error(`❌ Failed to refresh blacklist cache: ${error.message}`);
            // Continue with empty cache on error
        }
    }

    /**
     * Clear the cache
     */
    clearCache() {
        this.cache.clear();
        this.lastCacheUpdate = 0;
        console.log('🗑️  Blacklist cache cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            lastUpdate: new Date(this.lastCacheUpdate),
            isExpired: Date.now() - this.lastCacheUpdate > this.cacheExpiry
        };
    }
}

// Create a singleton instance
const blacklistManager = new BlacklistManager();

module.exports = blacklistManager;
