// Try to require nodemailer, but gracefully handle if it's not available
let nodemailer = null;
try {
  nodemailer = require('nodemailer');
} catch (error) {
  console.log('📧 nodemailer not available - email notifications disabled');
}

class NotificationService {
  constructor() {
    this.transporter = null;
    this.config = null;
    this.lastAlertTimes = new Map();
    this.isAvailable = !!nodemailer;
  }

  initialize(config) {
    if (!this.isAvailable) {
      console.log('📧 Email notifications not available (nodemailer missing)');
      return;
    }

    this.config = config;
    if (config?.smtp) {
      try {
        this.transporter = nodemailer.createTransporter(config.smtp);
      } catch (error) {
        console.warn('📧 Failed to initialize SMTP transporter:', error.message);
        this.isAvailable = false;
      }
    }
  }

  canSendAlert(alertKey) {
    if (!this.config?.alertCooldownMinutes) return true;
    
    const lastAlert = this.lastAlertTimes.get(alertKey);
    if (!lastAlert) return true;
    
    const now = new Date();
    const cooldownMs = this.config.alertCooldownMinutes * 60 * 1000;
    return (now.getTime() - lastAlert.getTime()) >= cooldownMs;
  }

  markAlertSent(alertKey) {
    this.lastAlertTimes.set(alertKey, new Date());
  }

  async sendAlert(alert) {
    if (!this.isAvailable) {
      // Silently skip if email is not available
      return false;
    }

    if (!this.transporter || !this.config) {
      console.warn('Notification service not initialized');
      return false;
    }

    const alertKey = `${alert.type}_${alert.brand || 'global'}`;
    
    if (!this.canSendAlert(alertKey)) {
      console.log(`Alert cooldown active for ${alertKey}`);
      return false;
    }

    try {
      const subject = this.getEmailSubject(alert);
      const body = this.getEmailBody(alert);

      const mailOptions = {
        from: this.config.fromEmail,
        to: this.config.toEmails.join(', '),
        subject,
        html: body
      };

      await this.transporter.sendMail(mailOptions);

      this.markAlertSent(alertKey);
      console.log(`✅ Alert sent successfully: ${alert.type}`);
      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  getEmailSubject(alert) {
    switch (alert.type) {
      case 'total_failure':
        return `🚨 Scraper Total Failure - ${alert.brand || 'Unknown Brand'}`;
      case 'critical_error':
        return `🔥 Scraper Critical Error - ${alert.runId || 'Unknown Run'}`;
      case 'brand_failure':
        return `⚠️ Scraper Brand Failed - ${alert.brand}`;
      case 'success_summary':
        return `✅ Scraper Daily Summary - ${new Date().toISOString().split('T')[0]}`;
      default:
        return '🤖 Scraper Alert';
    }
  }

  getEmailBody(alert) {
    const timestamp = new Date().toLocaleString();
    
    const baseStyle = `
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .content { padding: 0 20px; }
        .error { background: #fff5f5; padding: 15px; border-left: 4px solid #e53e3e; margin: 15px 0; }
        .info { background: #f0f9ff; padding: 15px; border-left: 4px solid #3182ce; margin: 15px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; }
      </style>
    `;

    switch (alert.type) {
      case 'total_failure':
        return `${baseStyle}
          <body>
            <div class="header">
              <h2>🚨 Scraper Total Failure Alert</h2>
              <p><strong>Brand:</strong> ${alert.brand}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
            </div>
            <div class="content">
              <div class="error">
                <h3>Failure Details</h3>
                <p><strong>Message:</strong> ${alert.message}</p>
                ${alert.error ? `<p><strong>Error:</strong> ${alert.error}</p>` : ''}
                <p><strong>Attempts:</strong> ${alert.attemptNumber}/${alert.totalAttempts}</p>
              </div>
              <p>The scraper has exhausted all retry attempts for this brand and will skip to the next brand.</p>
            </div>
            <div class="footer">
              <p>This alert was generated automatically by the Used Car Sales Scraper</p>
            </div>
          </body>
        `;

      case 'critical_error':
        return `${baseStyle}
          <body>
            <div class="header">
              <h2>🔥 Scraper Critical Error</h2>
              <p><strong>Run ID:</strong> ${alert.runId}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
            </div>
            <div class="content">
              <div class="error">
                <h3>Critical Error Details</h3>
                <p><strong>Message:</strong> ${alert.message}</p>
                ${alert.error ? `<p><strong>Error:</strong> ${alert.error}</p>` : ''}
              </div>
              <p>This is a critical system error that may require immediate attention.</p>
            </div>
            <div class="footer">
              <p>This alert was generated automatically by the Used Car Sales Scraper</p>
            </div>
          </body>
        `;

      case 'brand_failure':
        return `${baseStyle}
          <body>
            <div class="header">
              <h2>⚠️ Scraper Brand Failure</h2>
              <p><strong>Brand:</strong> ${alert.brand}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
            </div>
            <div class="content">
              <div class="info">
                <h3>Brand Failure Details</h3>
                <p><strong>Message:</strong> ${alert.message}</p>
                ${alert.error ? `<p><strong>Error:</strong> ${alert.error}</p>` : ''}
                <p><strong>Attempt:</strong> ${alert.attemptNumber}/${alert.totalAttempts}</p>
              </div>
              <p>The scraper encountered an issue with this brand but will retry.</p>
            </div>
            <div class="footer">
              <p>This alert was generated automatically by the Used Car Sales Scraper</p>
            </div>
          </body>
        `;

      case 'success_summary':
        return `${baseStyle}
          <body>
            <div class="header">
              <h2>✅ Daily Scraper Summary</h2>
              <p><strong>Date:</strong> ${new Date().toISOString().split('T')[0]}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
            </div>
            <div class="content">
              <div class="info">
                <h3>Summary Details</h3>
                <p>${alert.message}</p>
              </div>
            </div>
            <div class="footer">
              <p>This summary was generated automatically by the Used Car Sales Scraper</p>
            </div>
          </body>
        `;

      default:
        return `${baseStyle}
          <body>
            <div class="header">
              <h2>🤖 Scraper Alert</h2>
              <p><strong>Time:</strong> ${timestamp}</p>
            </div>
            <div class="content">
              <p>${alert.message}</p>
            </div>
            <div class="footer">
              <p>This alert was generated automatically by the Used Car Sales Scraper</p>
            </div>
          </body>
        `;
    }
  }
}

module.exports = { NotificationService };