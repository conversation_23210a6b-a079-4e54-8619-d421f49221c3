const fs = require('fs');
const path = require('path');
const batchUpsert = require('../database/batch-upsert');

// Load dotenv only if not running in Nuxt environment
if (!process.env.NUXT_ENV_INIT) {
  try {
    require('dotenv').config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

class DataOutputManager {
    constructor() {
        this.outputMode = process.env.OUTPUT_MODE || 'database'; // csv, database, both
        this.csvOutputDir = process.env.CSV_OUTPUT_DIR || './output';
        this.csvMergeOutputDir = process.env.CSV_MERGE_OUTPUT_DIR || './output_merge';

        // Ensure output directories exist
        this.ensureDirectoryExists(this.csvOutputDir);
        this.ensureDirectoryExists(this.csvMergeOutputDir);
    }

    /**
     * Ensure directory exists, create if it doesn't
     */
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * Generate timestamp string for filenames
     */
    generateTimestamp() {
        const jst = new Date(Date.now() + 9 * 3600 * 1000);
        const pad = n => n.toString().padStart(2, '0');
        const Y = jst.getUTCFullYear();
        const M = pad(jst.getUTCMonth() + 1);
        const D = pad(jst.getUTCDate());
        const h = pad(jst.getUTCHours());
        const m = pad(jst.getUTCMinutes());
        const s = pad(jst.getUTCSeconds());
        return `${Y}-${M}-${D}-${h}-${m}-${s}`;
    }

    /**
     * Convert car data to CSV format
     */
    convertToCSV(cars, header) {
        const csvBody = cars.map(c =>
            header.map(h => {
                const v = c[h] ?? '';
                return /[",\n]/.test(v) ? `"${v.replace(/"/g, '""')}"` : v;
            }).join(',')
        ).join('\n');

        return '\uFEFF' + header.join(',') + '\n' + csvBody;
    }

    /**
     * Save data to CSV file
     */
    async saveToCSV(cars, brandName, header) {
        if (this.outputMode === 'database') {
            return null; // Skip CSV output
        }

        const dateString = this.generateTimestamp();
        const filename = path.join(this.csvOutputDir, `${brandName}_${dateString}.csv`);
        const csvContent = this.convertToCSV(cars, header);

        fs.writeFileSync(filename, csvContent);
        console.log(`📄 CSV saved: ${filename} (${cars.length} records)`);

        return filename;
    }

    /**
     * Save data to database
     */
    async saveToDatabase(cars) {
        if (this.outputMode === 'csv') {
            return null; // Skip database output
        }

        try {
            const result = await batchUpsert.upsertCars(cars);
            console.log(`💾 Database saved: ${result.processed || (result.inserted + result.updated)} records processed`);

            if (result.errors.length > 0) {
                console.warn(`⚠️  ${result.errors.length} batch errors occurred`);
                // Optionally log detailed errors
                result.errors.forEach((error, index) => {
                    console.error(`   Error ${index + 1}: ${error.error}`);
                });
            }

            return result;
        } catch (error) {
            console.error('❌ Database save failed:', error.message);
            throw error;
        }
    }

    /**
     * Save data to both CSV and database based on configuration
     */
    async saveData(cars, brandName, header = null) {
        if (!Array.isArray(cars) || cars.length === 0) {
            console.log('⚠️  No data to save');
            return { csv: null, database: null };
        }

        // Default header if not provided
        if (!header) {
            header = [
                'car_name', 'detail', 'displacement', 'driving_system', 'engine_type', 'fix_history', 'handle',
                'image', 'insurance', 'maintenance', 'maker', 'mileage', 'mission', 'Mission_type', 'model', 'model_year',
                'price', 'price_range', 'price_range_500', 'price_range_1000', 'region', 'source', 'sourceURL', 'vehicle_inspection',
                'safe_equipment', 'comfort_equipment', 'interia_equipment', 'exteria_equipment'
            ];
        }

        const results = {};

        try {
            // Save to CSV
            if (this.outputMode === 'csv' || this.outputMode === 'both') {
                results.csv = await this.saveToCSV(cars, brandName, header);
            }

            // Save to database
            if (this.outputMode === 'database' || this.outputMode === 'both') {
                results.database = await this.saveToDatabase(cars);
            }

            console.log(`✅ Data saved successfully for ${brandName}: ${cars.length} records`);
            return results;

        } catch (error) {
            console.error(`❌ Failed to save data for ${brandName}:`, error.message);

            // If database fails but CSV is enabled, try to save to CSV as fallback
            if (this.outputMode === 'both' && !results.csv) {
                try {
                    console.log('🔄 Attempting CSV fallback...');
                    results.csv = await this.saveToCSV(cars, brandName, header);
                } catch (csvError) {
                    console.error('❌ CSV fallback also failed:', csvError.message);
                }
            }

            throw error;
        }
    }

    /**
     * Get current output mode
     */
    getOutputMode() {
        return this.outputMode;
    }

    /**
     * Set output mode
     */
    setOutputMode(mode) {
        if (['csv', 'database', 'both'].includes(mode)) {
            this.outputMode = mode;
            console.log(`📝 Output mode set to: ${mode}`);
        } else {
            throw new Error('Invalid output mode. Must be: csv, database, or both');
        }
    }
}

module.exports = new DataOutputManager();
