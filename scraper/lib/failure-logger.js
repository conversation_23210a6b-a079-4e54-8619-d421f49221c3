const fetch = require('node-fetch');

class FailureLogger {
  constructor() {
    this.directusUrl = process.env.DIRECTUS_URL || 'http://localhost:8056';
    this.runId = process.env.SCRAPER_RUN_ID || `run_${Date.now()}`;
  }

  /**
   * Log a scraper failure to the database via Directus API
   * @param {Object} params - Failure parameters
   * @param {string} params.brandName - Name of the brand that failed
   * @param {string} params.brandUrl - URL of the brand that failed  
   * @param {Error} params.error - The error that occurred
   * @param {string} params.errorType - Type of error (brand_failure, page_failure, detail_failure, etc.)
   * @param {number} params.attemptNumber - Current attempt number
   * @param {number} params.maxAttempts - Maximum number of attempts
   * @param {Object} params.context - Additional context information
   */
  async logFailure({
    brandName,
    brandUrl = '',
    error,
    errorType = 'unknown',
    attemptNumber = 1,
    maxAttempts = 3,
    context = {}
  }) {
    try {
      const failureData = {
        run_id: this.runId,
        brand_name: brandName,
        brand_url: brandUrl,
        error_message: error.message || String(error),
        error_stack: error.stack || '',
        error_type: errorType,
        attempt_number: attemptNumber,
        max_attempts: maxAttempts,
        context: {
          ...context,
          timestamp: new Date().toISOString(),
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          nodeVersion: process.version,
          platform: process.platform
        }
      };

      const response = await fetch(`${this.directusUrl}/items/scraper_failures`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(failureData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to log failure to Directus: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log(`📝 Logged failure for ${brandName} to database (ID: ${result.data.id})`);
      return result.data;

    } catch (loggingError) {
      console.error('❌ Failed to log failure to database:', loggingError.message);
      // Fallback to console logging if database logging fails
      console.error(`📝 FALLBACK LOG - Brand: ${brandName}, Error: ${error.message}, Type: ${errorType}, Attempt: ${attemptNumber}/${maxAttempts}`);
      return null;
    }
  }

  /**
   * Get all failures for the current run
   */
  async getFailures() {
    try {
      const response = await fetch(`${this.directusUrl}/items/scraper_failures?filter[run_id][_eq]=${this.runId}&sort=-created_at`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch failures: ${response.status}`);
      }

      const result = await response.json();
      return result.data || [];

    } catch (error) {
      console.error('❌ Failed to fetch failures from database:', error.message);
      return [];
    }
  }

  /**
   * Get failure statistics for the current run
   */
  async getFailureStats() {
    try {
      const failures = await this.getFailures();
      
      const stats = {
        totalFailures: failures.length,
        failedBrands: new Set(failures.map(f => f.brand_name)).size,
        errorTypes: {}
      };

      failures.forEach(failure => {
        const type = failure.error_type;
        if (!stats.errorTypes[type]) {
          stats.errorTypes[type] = 0;
        }
        stats.errorTypes[type]++;
      });

      return stats;

    } catch (error) {
      console.error('❌ Failed to get failure stats:', error.message);
      return { totalFailures: 0, failedBrands: 0, errorTypes: {} };
    }
  }
}

module.exports = FailureLogger;