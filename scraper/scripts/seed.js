#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const batchUpsert = require('../database/batch-upsert');
const dbConnection = require('../database/connection');

class DatabaseSeeder {
    constructor() {
        this.csvDir = process.env.CSV_OUTPUT_DIR || './output';
        this.mergedCsvDir = process.env.CSV_MERGE_OUTPUT_DIR || './output_merge';
    }

    /**
     * Get all CSV files from a directory
     */
    getCSVFiles(directory) {
        if (!fs.existsSync(directory)) {
            console.log(`⚠️  Directory not found: ${directory}`);
            return [];
        }

        return fs.readdirSync(directory)
            .filter(file => file.toLowerCase().endsWith('.csv'))
            .map(file => path.join(directory, file));
    }

    /**
     * Parse a single CSV file
     */
    parseCSVFile(filePath) {
        console.log(`📄 Parsing CSV file: ${path.basename(filePath)}`);
        
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Remove BOM if present
            const cleanContent = content.replace(/^\uFEFF/, '');
            
            // Parse CSV with headers
            const records = parse(cleanContent, {
                columns: true,
                skip_empty_lines: true,
                trim: true
            });
            
            console.log(`   ✅ Parsed ${records.length} records`);
            return records;
            
        } catch (error) {
            console.error(`   ❌ Failed to parse ${filePath}:`, error.message);
            return [];
        }
    }

    /**
     * Seed database from CSV files in a directory
     */
    async seedFromDirectory(directory, description) {
        console.log(`\n🌱 Seeding from ${description}...`);
        
        const csvFiles = this.getCSVFiles(directory);
        if (csvFiles.length === 0) {
            console.log(`   ⚠️  No CSV files found in ${directory}`);
            return { totalRecords: 0, inserted: 0, updated: 0 };
        }

        console.log(`   📋 Found ${csvFiles.length} CSV files`);
        
        let totalRecords = 0;
        let totalInserted = 0;
        let totalUpdated = 0;
        const errors = [];

        for (const csvFile of csvFiles) {
            try {
                const records = this.parseCSVFile(csvFile);
                if (records.length === 0) continue;

                totalRecords += records.length;
                
                const result = await batchUpsert.upsertCars(records);
                totalInserted += result.inserted;
                totalUpdated += result.updated;
                
                if (result.errors.length > 0) {
                    errors.push(...result.errors);
                }
                
                console.log(`   ✅ ${path.basename(csvFile)}: ${result.inserted} inserted, ${result.updated} updated`);
                
            } catch (error) {
                console.error(`   ❌ Failed to process ${csvFile}:`, error.message);
                errors.push({ file: csvFile, error: error.message });
            }
        }

        console.log(`   🎉 ${description} completed: ${totalInserted} inserted, ${totalUpdated} updated from ${totalRecords} records`);
        
        if (errors.length > 0) {
            console.warn(`   ⚠️  ${errors.length} errors occurred during seeding`);
        }

        return { totalRecords, inserted: totalInserted, updated: totalUpdated, errors };
    }

    /**
     * Seed database from all available CSV sources
     */
    async seedAll() {
        console.log('🚀 Starting database seeding...');
        
        try {
            // Connect to database
            await dbConnection.connect();
            
            // Check if table exists
            const tableCheck = await dbConnection.query(`
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'used_cars'
                )
            `);
            
            if (!tableCheck.rows[0].exists) {
                throw new Error('used_cars table does not exist. Please run migrations first.');
            }

            let grandTotal = {
                totalRecords: 0,
                inserted: 0,
                updated: 0,
                errors: []
            };

            // Seed from merged CSV files first (these are usually cleaner)
            if (fs.existsSync(this.mergedCsvDir)) {
                const mergedResult = await this.seedFromDirectory(this.mergedCsvDir, 'merged CSV files');
                grandTotal.totalRecords += mergedResult.totalRecords;
                grandTotal.inserted += mergedResult.inserted;
                grandTotal.updated += mergedResult.updated;
                grandTotal.errors.push(...mergedResult.errors);
            }

            // Seed from individual CSV files
            if (fs.existsSync(this.csvDir)) {
                const individualResult = await this.seedFromDirectory(this.csvDir, 'individual CSV files');
                grandTotal.totalRecords += individualResult.totalRecords;
                grandTotal.inserted += individualResult.inserted;
                grandTotal.updated += individualResult.updated;
                grandTotal.errors.push(...individualResult.errors);
            }

            console.log(`\n🎉 Seeding completed successfully!`);
            console.log(`   📊 Total records processed: ${grandTotal.totalRecords}`);
            console.log(`   ➕ Records inserted: ${grandTotal.inserted}`);
            console.log(`   🔄 Records updated: ${grandTotal.updated}`);
            
            if (grandTotal.errors.length > 0) {
                console.log(`   ⚠️  Total errors: ${grandTotal.errors.length}`);
            }

            // Show final database stats
            await this.showDatabaseStats();
            
        } catch (error) {
            console.error('❌ Seeding failed:', error.message);
            process.exit(1);
        } finally {
            await dbConnection.close();
        }
    }

    /**
     * Show database statistics
     */
    async showDatabaseStats() {
        console.log('\n📈 Database Statistics:');
        
        try {
            // Total count
            const totalResult = await dbConnection.query('SELECT COUNT(*) as total FROM used_cars');
            console.log(`   Total cars: ${totalResult.rows[0].total}`);
            
            // Count by maker
            const makerResult = await dbConnection.query(`
                SELECT maker, COUNT(*) as count 
                FROM used_cars 
                WHERE maker IS NOT NULL 
                GROUP BY maker 
                ORDER BY count DESC 
                LIMIT 10
            `);
            
            console.log('   Top makers:');
            makerResult.rows.forEach(row => {
                console.log(`     ${row.maker}: ${row.count}`);
            });
            
            // Recent additions
            const recentResult = await dbConnection.query(`
                SELECT COUNT(*) as recent_count 
                FROM used_cars 
                WHERE created_at > NOW() - INTERVAL '1 day'
            `);
            console.log(`   Added in last 24h: ${recentResult.rows[0].recent_count}`);
            
        } catch (error) {
            console.error('❌ Failed to get database stats:', error.message);
        }
    }

    /**
     * Clear all data from the database
     */
    async clearAll() {
        console.log('⚠️  Clearing all data from database...');
        
        try {
            await dbConnection.connect();
            
            const result = await dbConnection.query('DELETE FROM used_cars');
            console.log(`🗑️  Deleted ${result.rowCount} records`);
            
        } catch (error) {
            console.error('❌ Failed to clear database:', error.message);
            process.exit(1);
        } finally {
            await dbConnection.close();
        }
    }
}

// CLI interface
async function main() {
    const seeder = new DatabaseSeeder();
    const command = process.argv[2];
    
    switch (command) {
        case 'seed':
            await seeder.seedAll();
            break;
        case 'clear':
            await seeder.clearAll();
            break;
        case 'stats':
            await dbConnection.connect();
            await seeder.showDatabaseStats();
            await dbConnection.close();
            break;
        default:
            console.log(`
Usage: node scripts/seed.js <command>

Commands:
  seed     Import all CSV files into the database
  clear    Remove all data from the database
  stats    Show database statistics

Examples:
  node scripts/seed.js seed
  node scripts/seed.js clear
  node scripts/seed.js stats
            `);
            process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Seeding script failed:', error.message);
        process.exit(1);
    });
}

module.exports = DatabaseSeeder;
