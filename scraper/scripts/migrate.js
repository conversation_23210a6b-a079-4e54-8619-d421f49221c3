#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const dbConnection = require('../database/connection');

class DatabaseMigrator {
    constructor() {
        this.migrationsDir = path.join(__dirname, '../database/init');
    }

    /**
     * Get all SQL migration files in order
     */
    getMigrationFiles() {
        if (!fs.existsSync(this.migrationsDir)) {
            throw new Error(`Migrations directory not found: ${this.migrationsDir}`);
        }

        return fs.readdirSync(this.migrationsDir)
            .filter(file => file.endsWith('.sql'))
            .sort(); // Files should be named with prefixes like 01-, 02-, etc.
    }

    /**
     * Execute a single migration file
     */
    async executeMigration(filename) {
        const filePath = path.join(this.migrationsDir, filename);
        const sql = fs.readFileSync(filePath, 'utf8');
        
        console.log(`📄 Executing migration: ${filename}`);
        
        try {
            // Split by semicolon and execute each statement
            const statements = sql
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);

            for (const statement of statements) {
                if (statement.trim()) {
                    await dbConnection.query(statement);
                }
            }
            
            console.log(`✅ Migration completed: ${filename}`);
        } catch (error) {
            console.error(`❌ Migration failed: ${filename}`);
            throw error;
        }
    }

    /**
     * Run all migrations
     */
    async migrate() {
        console.log('🚀 Starting database migration...');
        
        try {
            // Connect to database
            await dbConnection.connect();
            
            // Get migration files
            const migrationFiles = this.getMigrationFiles();
            console.log(`📋 Found ${migrationFiles.length} migration files`);
            
            // Execute each migration
            for (const file of migrationFiles) {
                await this.executeMigration(file);
            }
            
            console.log('🎉 All migrations completed successfully!');
            
            // Test the schema
            await this.testSchema();
            
        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            process.exit(1);
        } finally {
            await dbConnection.close();
        }
    }

    /**
     * Test the schema by running a simple query
     */
    async testSchema() {
        console.log('🧪 Testing schema...');
        
        try {
            // Test table exists and basic structure
            const result = await dbConnection.query(`
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'used_cars'
                ORDER BY ordinal_position
            `);
            
            console.log(`✅ Schema test passed: used_cars table has ${result.rows.length} columns`);
            
            // Test basic insert/select (will be rolled back)
            await dbConnection.query('BEGIN');
            
            const testInsert = await dbConnection.query(`
                INSERT INTO used_cars (car_name, maker, source_url) 
                VALUES ('Test Car', 'Test Maker', 'http://test.com/test')
                RETURNING id
            `);
            
            const testSelect = await dbConnection.query(`
                SELECT id, car_name, maker, created_at 
                FROM used_cars 
                WHERE id = $1
            `, [testInsert.rows[0].id]);
            
            await dbConnection.query('ROLLBACK');
            
            if (testSelect.rows.length === 1) {
                console.log('✅ Insert/Select test passed');
            } else {
                throw new Error('Insert/Select test failed');
            }
            
        } catch (error) {
            await dbConnection.query('ROLLBACK').catch(() => {}); // Ignore rollback errors
            throw error;
        }
    }

    /**
     * Reset database (drop and recreate schema)
     */
    async reset() {
        console.log('⚠️  Resetting database...');
        
        try {
            await dbConnection.connect();
            
            // Drop the table if it exists
            await dbConnection.query('DROP TABLE IF EXISTS used_cars CASCADE');
            console.log('🗑️  Dropped existing tables');
            
            // Run migrations
            await this.migrate();
            
        } catch (error) {
            console.error('❌ Database reset failed:', error.message);
            process.exit(1);
        }
    }
}

// CLI interface
async function main() {
    const migrator = new DatabaseMigrator();
    const command = process.argv[2];
    
    switch (command) {
        case 'migrate':
            await migrator.migrate();
            break;
        case 'reset':
            await migrator.reset();
            break;
        default:
            console.log(`
Usage: node scripts/migrate.js <command>

Commands:
  migrate    Run all pending migrations
  reset      Drop all tables and run migrations from scratch

Examples:
  node scripts/migrate.js migrate
  node scripts/migrate.js reset
            `);
            process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Migration script failed:', error.message);
        process.exit(1);
    });
}

module.exports = DatabaseMigrator;
