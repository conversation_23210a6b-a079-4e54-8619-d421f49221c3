#!/usr/bin/env node

/**
 * Simple Test Runner
 * 
 * Runs all available tests for the project
 */

const path = require('path');
const fs = require('fs');

class TestRunner {
    constructor() {
        this.testDir = path.join(__dirname, '../tests');
        this.totalTests = 0;
        this.totalPassed = 0;
        this.testFiles = [];
    }

    async run() {
        console.log('🧪 Starting Test Suite\n');

        try {
            await this.discoverTests();
            await this.runTests();
            this.printSummary();
        } catch (error) {
            console.error(`💥 Test runner failed: ${error.message}`);
            process.exit(1);
        }
    }

    async discoverTests() {
        if (!fs.existsSync(this.testDir)) {
            console.log('📁 No tests directory found');
            return;
        }

        const files = fs.readdirSync(this.testDir);
        this.testFiles = files.filter(file => file.endsWith('.test.js'));

        console.log(`📋 Found ${this.testFiles.length} test files:`);
        this.testFiles.forEach(file => {
            console.log(`   • ${file}`);
        });
        console.log('');
    }

    async runTests() {
        for (const testFile of this.testFiles) {
            const testPath = path.join(this.testDir, testFile);
            console.log(`🏃 Running ${testFile}...`);
            
            try {
                const TestClass = require(testPath);
                const testInstance = new TestClass();
                
                if (typeof testInstance.runAllTests === 'function') {
                    await testInstance.runAllTests();
                    
                    // Extract results if available
                    if (testInstance.testCount && testInstance.passCount !== undefined) {
                        this.totalTests += testInstance.testCount;
                        this.totalPassed += testInstance.passCount;
                    }
                } else {
                    console.warn(`⚠️  ${testFile} does not have a runAllTests method`);
                }
                
            } catch (error) {
                console.error(`❌ Failed to run ${testFile}: ${error.message}`);
            }
            
            console.log(''); // Add spacing between test files
        }
    }

    printSummary() {
        console.log('=' .repeat(50));
        console.log('📊 OVERALL TEST SUMMARY');
        console.log('=' .repeat(50));
        
        if (this.totalTests === 0) {
            console.log('⚠️  No tests were executed');
            return;
        }

        console.log(`Total Tests: ${this.totalTests}`);
        console.log(`Passed: ${this.totalPassed}`);
        console.log(`Failed: ${this.totalTests - this.totalPassed}`);
        console.log(`Success Rate: ${((this.totalPassed / this.totalTests) * 100).toFixed(1)}%`);

        if (this.totalPassed === this.totalTests) {
            console.log('\n🎉 ALL TESTS PASSED!');
        } else {
            console.log(`\n💥 ${this.totalTests - this.totalPassed} TESTS FAILED`);
            process.exit(1);
        }
    }

    showHelp() {
        console.log(`
🧪 Test Runner

USAGE:
  node scripts/test.js

DESCRIPTION:
  Discovers and runs all test files in the tests/ directory.
  Test files should end with .test.js and export a class with a runAllTests() method.

EXAMPLES:
  # Run all tests
  node scripts/test.js

  # Run specific test file directly
  node tests/blacklist-manager.test.js
`);
    }
}

// Run if this file is executed directly
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        const runner = new TestRunner();
        runner.showHelp();
        process.exit(0);
    }

    const runner = new TestRunner();
    runner.run().catch(error => {
        console.error(`💥 Test runner crashed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = TestRunner;
