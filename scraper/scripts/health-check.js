#!/usr/bin/env node

const dbConnection = require('../database/connection');

async function healthCheck() {
    console.log('🏥 Running health check...\n');
    
    try {
        // Database health check
        console.log('📊 Database Health:');
        const dbHealth = await dbConnection.healthCheck();
        
        if (dbHealth.status === 'healthy') {
            console.log('   ✅ Database: Connected and responsive');
            console.log(`   🕐 Server time: ${dbHealth.timestamp}`);
        } else {
            console.log('   ❌ Database: Unhealthy');
            console.log(`   🚨 Error: ${dbHealth.error}`);
        }
        
        // Table structure check
        console.log('\n📋 Table Structure:');
        try {
            const tableInfo = await dbConnection.query(`
                SELECT 
                    column_name, 
                    data_type, 
                    is_nullable,
                    column_default
                FROM information_schema.columns 
                WHERE table_name = 'used_cars'
                ORDER BY ordinal_position
            `);
            
            if (tableInfo.rows.length > 0) {
                console.log('   ✅ used_cars table exists');
                console.log(`   📊 Columns: ${tableInfo.rows.length}`);
                
                // Show key columns
                const keyColumns = ['id', 'car_name', 'maker', 'source_url', 'created_at'];
                keyColumns.forEach(col => {
                    const column = tableInfo.rows.find(row => row.column_name === col);
                    if (column) {
                        console.log(`   📌 ${col}: ${column.data_type}`);
                    }
                });
            } else {
                console.log('   ❌ used_cars table not found');
            }
        } catch (error) {
            console.log(`   ❌ Table check failed: ${error.message}`);
        }
        
        // Data statistics
        console.log('\n📈 Data Statistics:');
        try {
            const stats = await dbConnection.query(`
                SELECT 
                    COUNT(*) as total_cars,
                    COUNT(DISTINCT maker) as unique_makers,
                    COUNT(DISTINCT source) as unique_sources,
                    MIN(created_at) as oldest_record,
                    MAX(created_at) as newest_record
                FROM used_cars
            `);
            
            if (stats.rows.length > 0) {
                const stat = stats.rows[0];
                console.log(`   🚗 Total cars: ${stat.total_cars}`);
                console.log(`   🏭 Unique makers: ${stat.unique_makers}`);
                console.log(`   📡 Unique sources: ${stat.unique_sources}`);
                if (stat.oldest_record) {
                    console.log(`   📅 Oldest record: ${new Date(stat.oldest_record).toLocaleDateString()}`);
                    console.log(`   📅 Newest record: ${new Date(stat.newest_record).toLocaleDateString()}`);
                }
            }
        } catch (error) {
            console.log(`   ❌ Stats check failed: ${error.message}`);
        }
        
        // Index health
        console.log('\n🔍 Index Health:');
        try {
            const indexes = await dbConnection.query(`
                SELECT 
                    indexname,
                    tablename,
                    indexdef
                FROM pg_indexes 
                WHERE tablename = 'used_cars'
                ORDER BY indexname
            `);
            
            console.log(`   📊 Total indexes: ${indexes.rows.length}`);
            indexes.rows.forEach(idx => {
                console.log(`   📌 ${idx.indexname}`);
            });
        } catch (error) {
            console.log(`   ❌ Index check failed: ${error.message}`);
        }
        
        // Recent activity
        console.log('\n⏰ Recent Activity:');
        try {
            const recentActivity = await dbConnection.query(`
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as records_added
                FROM used_cars 
                WHERE created_at > NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
                ORDER BY date DESC
                LIMIT 7
            `);
            
            if (recentActivity.rows.length > 0) {
                console.log('   📊 Records added in last 7 days:');
                recentActivity.rows.forEach(row => {
                    console.log(`   📅 ${row.date}: ${row.records_added} records`);
                });
            } else {
                console.log('   ℹ️  No records added in the last 7 days');
            }
        } catch (error) {
            console.log(`   ❌ Activity check failed: ${error.message}`);
        }
        
        console.log('\n🎉 Health check completed!');
        
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        process.exit(1);
    } finally {
        await dbConnection.close();
    }
}

// Run if called directly
if (require.main === module) {
    healthCheck().catch(error => {
        console.error('❌ Health check script failed:', error.message);
        process.exit(1);
    });
}

module.exports = healthCheck;
