#!/usr/bin/env node

const dbConnection = require('../database/connection');

async function removeUniqueConstraint() {
    console.log('🔧 Removing unique constraint on source_url...');
    
    try {
        await dbConnection.connect();
        
        // Check if the constraint exists first
        const constraintCheck = await dbConnection.query(`
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'used_cars' 
            AND constraint_type = 'UNIQUE'
            AND constraint_name LIKE '%source_url%'
        `);
        
        if (constraintCheck.rows.length > 0) {
            const constraintName = constraintCheck.rows[0].constraint_name;
            console.log(`📋 Found unique constraint: ${constraintName}`);
            
            // Drop the constraint
            await dbConnection.query(`
                ALTER TABLE used_cars 
                DROP CONSTRAINT ${constraintName}
            `);
            
            console.log('✅ Unique constraint removed successfully');
        } else {
            console.log('ℹ️  No unique constraint found on source_url');
        }
        
        // Verify the constraint is gone
        const verifyCheck = await dbConnection.query(`
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'used_cars' 
            AND constraint_type = 'UNIQUE'
        `);
        
        console.log(`📊 Remaining unique constraints: ${verifyCheck.rows.length}`);
        verifyCheck.rows.forEach(row => {
            console.log(`   - ${row.constraint_name}`);
        });
        
        console.log('🎉 Migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        process.exit(1);
    } finally {
        await dbConnection.close();
    }
}

// Run if called directly
if (require.main === module) {
    removeUniqueConstraint().catch(error => {
        console.error('❌ Script failed:', error.message);
        process.exit(1);
    });
}

module.exports = removeUniqueConstraint;
