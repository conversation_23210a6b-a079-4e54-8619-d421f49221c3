const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const dataOutputManager = require('./lib/data-output-manager');

// Load dotenv only if not running in Nuxt environment
if (!process.env.NUXT_ENV_INIT) {
  try {
    require('dotenv').config();
  } catch (error) {
    console.log('Note: dotenv not available, using system environment variables');
  }
}

// 全メーカー定義（主要メーカー例）
const brands = [
  { name: "レクサス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LE" },
  { name: "トヨタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TO" },
  { name: "日産", url: "https://www.carsensor.net/usedcar/search.php?BRDC=NI" },
  { name: "ホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HO" },
  { name: "マツダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MA" },
  { name: "スバル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SB" },
  { name: "スズキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SZ" },
  { name: "三菱", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MI" },
  { name: "ダイハツ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DA" },
  { name: "いすゞ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=IS" },
  { name: "光岡自動車", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MT" },
  { name: "トミーカイラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TM" },
  { name: "日野", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HI" },
  { name: "UDトラックス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UD" },
  { name: "三菱ふそう", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FU" },
  { name: "国産車その他", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ZJ" },
  { name: "メルセデスベンツ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ME" },
  { name: "メルセデスAMG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AM" },
  { name: "メルセデス・マイバッハ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MM" },
  { name: "AMG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AG" },
  { name: "マイバッハ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MY" },
  { name: "スマート", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MC" },
  { name: "BMW", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BM" },
  { name: "BMWアルピナ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AL" },
  { name: "アウディ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AD" },
  { name: "フォルクスワーゲン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VW" },
  { name: "オペル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=OP" },
  { name: "ポルシェ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PO" },
  { name: "ルーフ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RU" },
  { name: "ブラバス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BR" },
  { name: "イエス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=YE" },
  { name: "カールソン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CL" },
  { name: "アルテガ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AA" },
  { name: "バーストナー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BN" },
  { name: "ミニ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MN" },
  { name: "キャデラック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CA" },
  { name: "シボレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CH" },
  { name: "ビュイック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BU" },
  { name: "ポンテアック", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PN" },
  { name: "サターン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ST" },
  { name: "ハマー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HM" },
  { name: "GMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=GC" },
  { name: "フォード", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FO" },
  { name: "リンカーン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LI" },
  { name: "マーキュリー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MK" },
  { name: "サリーン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SL" },
  { name: "クライスラー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CR" },
  { name: "ダッジ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DG" },
  { name: "プリムス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PR" },
  { name: "AMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AC" },
  { name: "AMCジープ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AJ" },
  { name: "ジープ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=JE" },
  { name: "オールズモビル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=OL" },
  { name: "ウィネベーゴ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=WN" },
  { name: "DMC", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DC" },
  { name: "テスラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TS" },
  { name: "米国レクサス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UL" },
  { name: "米国インフィニティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UN" },
  { name: "米国アキュラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UA" },
  { name: "米国トヨタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UT" },
  { name: "米国サイオン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SI" },
  { name: "米国日産", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UN" },
  { name: "米国ホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UH" },
  { name: "米国マツダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UM" },
  { name: "米国スバル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UR" },
  { name: "米国スズキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=US" },
  { name: "米国三菱", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UB" },
  { name: "カナダホンダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CD" },
  { name: "ロールスロイス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RR" },
  { name: "ベントレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BE" },
  { name: "ジャガー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=JA" },
  { name: "デイムラー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DI" },
  { name: "ランドローバー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LR" },
  { name: "アストンマーティン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AS" },
  { name: "ロータス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RO" },
  { name: "ロンドンタクシー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LT" },
  { name: "マクラーレン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ML" },
  { name: "MG", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MG" },
  { name: "ローバー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RV" },
  { name: "オースチン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AU" },
  { name: "モーリス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MR" },
  { name: "BL", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BL" },
  { name: "モーク", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MQ" },
  { name: "マーコス", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MF" },
  { name: "バンデンプラ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VP" },
  { name: "ライレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RI" },
  { name: "ケータハム", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CT" },
  { name: "ウエストフィールド", url: "https://www.carsensor.net/usedcar/search.php?BRDC=WE" },
  { name: "モーガン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MO" },
  { name: "パンサー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PT" },
  { name: "トライアンフ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=TR" },
  { name: "ヒーレー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HE" },
  { name: "ジネッタ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=GI" },
  { name: "ボルボ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=VO" },
  { name: "サーブ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SA" },
  { name: "ケーニッグゼグ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KO" },
  { name: "スカニア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SN" },
  { name: "プジョー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=PE" },
  { name: "ルノー", url: "https://www.carsensor.net/usedcar/search.php?BRDC=RE" },
  { name: "シトロエン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=CI" },
  { name: "DSオートモビル", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DS" },
  { name: "アルピーヌ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AN" },
  { name: "フィアット", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FI" },
  { name: "アルファ　ロメオ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AF" },
  { name: "フェラーリ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=FE" },
  { name: "ランボルギーニ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LG" },
  { name: "マセラティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=MS" },
  { name: "ランチア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LA" },
  { name: "アウトビアンキ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AT" },
  { name: "アバルト", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AB" },
  { name: "イノチェンティ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=IN" },
  { name: "デトマソ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DE" },
  { name: "KTM", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KT" },
  { name: "セアト", url: "https://www.carsensor.net/usedcar/search.php?BRDC=SE" },
  { name: "フータン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HA" },
  { name: "ドンカーブート", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DV" },
  { name: "ダチア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=DB" },
  { name: "アドリア", url: "https://www.carsensor.net/usedcar/search.php?BRDC=AE" },
  { name: "ラーダ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=LD" },
  { name: "ワズ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=UZ" },
  { name: "ホールデン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HL" },
  { name: "BYD", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BY" },
  { name: "ヒョンデ", url: "https://www.carsensor.net/usedcar/search.php?BRDC=HD" },
  { name: "起亜", url: "https://www.carsensor.net/usedcar/search.php?BRDC=KI" },
  { name: "バーキン", url: "https://www.carsensor.net/usedcar/search.php?BRDC=BI" },
  { name: "輸入車その他", url: "https://www.carsensor.net/usedcar/search.php?BRDC=ZZ" }
].map(brand => {
  const urlObj = new URL(brand.url);
  urlObj.searchParams.set('YMAX', '2005'); // ここで条件を指定
  return { ...brand, url: urlObj.toString() };
});

function getPriceRange100(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 2000) return '2000万円~';
  const min = Math.floor((val - 1) / 100) * 100 + 1;
  const max = Math.ceil(val / 100) * 100;
  return `${min}~${max}万円`;
}

function getPriceRange500(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 10000) return '1億1円~';
  if (val > 9500) return '9501万円~1億円';
  const min = Math.floor((val - 1) / 500) * 500 + 1;
  const max = Math.ceil(val / 500) * 500;
  if (max === 10000) return '9501万円~1億円';
  return `${min}~${max}万円`;
}

function getPriceRange1000(val) {
  if (!val || isNaN(val) || val === 0) return '応談';
  if (val > 20000) return '2億1円~';
  if (val > 19000) return '1億9001~2億円';
  if (val > 18000) return '1億8001~1億9000万円';
  const min = Math.floor((val - 1) / 1000) * 1000 + 1;
  const max = Math.ceil(val / 1000) * 1000;
  if (max === 20000) return '1億9001~2億円';
  if (max === 19000) return '1億8001~1億9000万円';
  return `${min}~${max}万円`;
}

(async () => {

  //── 出力フォルダを準備
  const outputDir = path.resolve(__dirname, 'output');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  //── ブラウザ起動
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  });
  const page = await context.newPage();

  // Initialize data output manager
  const outputManager = dataOutputManager;
  console.log(`📊 Output mode: ${outputManager.outputMode}`);

  //── 詳細ページから仕様を取ってくる関数（ラベルマッチ方式）
  const getDetails = async (detailPage, url, retries = 3) => {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await detailPage.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });

        // 画像ギャラリーやimgが現れるまで待つ
        await detailPage.waitForFunction(() => {
          return (
            document.querySelector('a[data-photo]') ||
            Array.from(document.querySelectorAll('img')).some(img =>
              (img.getAttribute('data-src') && !img.getAttribute('data-src').includes('animation_M.gif')) ||
              (img.getAttribute('src') && !img.getAttribute('src').includes('animation_M.gif'))
            )
          );
        }, { timeout: 10000 });

        const data = await detailPage.evaluate(() => {
          // 画像URL抽出
          const imageUrls = new Set();
          document.querySelectorAll('a[data-photo]').forEach(a => {
            const url = a.getAttribute('data-photo');
            if (url && !url.includes('animation_M.gif')) imageUrls.add(url);
          });
          document.querySelectorAll('img').forEach(img => {
            const dataSrc = img.getAttribute('data-src');
            if (dataSrc && !dataSrc.includes('animation_M.gif')) imageUrls.add(dataSrc);
            const src = img.getAttribute('src');
            if (src && !src.includes('animation_M.gif')) imageUrls.add(src);
          });
          // 最大10個に制限
          const images = Array.from(imageUrls).slice(0, 10);

          // 仕様情報（従来通り）
          const heading = document.getElementById('sec-kihon');
          if (!heading) return { images };
          const section = heading.closest('section');
          if (!section) return { images };
          const table = section.querySelector('div.defaultTable table.defaultTable__table');
          if (!table) return { images };
          const rows = Array.from(table.querySelectorAll('tbody > tr'));

          const td0 = rows[0]?.querySelectorAll('td.defaultTable__description');
          let driving_system = td0?.[1]?.textContent.trim() || '指定なし';
          if (driving_system === '不明') driving_system = '指定なし';

          const tdHandle = rows[1]?.querySelectorAll('td.defaultTable__description');
          let handle = tdHandle?.[1]?.textContent.trim() || '指定なし';
          if (handle === '不明') handle = '指定なし';

          const tdEngine = rows[4]?.querySelectorAll('td.defaultTable__description');
          let engine_type = tdEngine?.[0]?.textContent.trim() || 'その他';
          if (engine_type === '不明') engine_type = 'その他';

          const cats = Array.from(document.querySelectorAll('div.equipmentList__category'));
          const getEquip = idx => {
            if (!cats[idx]) return 'なし';
            const items = Array.from(cats[idx].querySelectorAll('li.equipmentList__item--active'));
            return items.map(el => el.textContent.trim()).join('\n') || 'なし';
          };

          let modelText = document.querySelector('div.toPageTop__inner')
            ?.textContent.split('・')[1]?.trim() || '指定なし';
          if (modelText === '不明') modelText = '指定なし';


          return {
            images: Array.from(imageUrls),
            driving_system,
            engine_type,
            handle,
            safeequipment: getEquip(0),
            comfortequipment: getEquip(1),
            interiaequipment: getEquip(2),
            exteriaequipment: getEquip(3),
            modelText
          };
        });

        if (data) {
          if (!data.images) data.images = [];
          for (const k of ['driving_system', 'engine_type', 'handle']) {
            if (!data[k]) data[k] = '';
          }
          return data;
        }
      } catch (e) {
        console.warn(`getDetails リトライ ${attempt}/${retries} 失敗: ${e.message}`);
        await detailPage.waitForTimeout(1000);
      }
    }
    console.error('getDetails 全 attempts 失敗:', url);
    return { images: [] };
  };

  // ループの外で全車両データ格納用配列を用意(1つのCSVに保存)
  const allCars = [];


  //── ブランドごとにスクレイピング
  for (const { name: brandName, url: startUrl } of brands) {
    console.log(`🚀 Scraping for ${brandName}: ${startUrl}`);
    let pageCount = 1;
    await page.goto(startUrl, { waitUntil: 'domcontentloaded' });

    // ★ zeroHit（検索結果0件）を検出し、次ブランドへスキップ
    const zeroHit = await page.$('div.zeroHit');
    if (zeroHit) {
      console.log(`🔎 ブランド「${brandName}」は検索結果0件のためスキップ`);
      continue; // 次のブランドへ
    }

    while (true) {
      await page.waitForSelector(
        '#carList > div > div.js-mainCassette > div.js_listTableCassette > div.cassetteMain',
        { timeout: 60000 }
      );

      // 車リストを取得
      const cars = await page.evaluate(() => {
        const get = (container, sel) =>
          container.querySelector(sel)?.textContent.trim().replace(/\u00A0/g, ' ') || null;
        const containers = document.querySelectorAll(
          '#carList > div > div.js-mainCassette > div.js_listTableCassette > div.cassetteMain'
        );

        return Array.from(containers).map(container => {
          const rawPrice = get(container, 'div.cassetteMain__priceInfo span.totalPrice__mainPriceNum');
          const priceStr = rawPrice === '応談' ? '0' : rawPrice;

          const y1 = get(container, 'div.cassetteMain__specInfo > dl > div:nth-child(1) > dd > span.specList__emphasisData') || '';
          const y2 = get(container, 'div.cassetteMain__specInfo > dl > div:nth-child(1) > dd > span.specList__jpYear') || '';
          const rawYear = y1 + y2;
          let modelYear = null;
          if (rawYear) {
            const m = rawYear.match(/^(\d{4})/);
            modelYear = m ? `${m[1]}/1/1` : rawYear;
          }

          return {
            car_name: get(container, 'h3 > a'),
            detail: get(container, 'p.cassetteMain__subText'),
            displacement: get(container, 'div:nth-child(7) > dd')?.replace(/CC/gi, '').trim(),
            fix_history: get(container, 'div:nth-child(4) > dd'),
            image: '',
            insurance: get(container, 'div:nth-child(5) > dd'),
            maintenance: get(container, 'div:nth-child(6) > dd'),
            maker: get(container, 'div.cassetteMain__carInfoContainer > p'),
            mileage: parseFloat(get(container, 'div:nth-child(2) > dd > span') || '0') * 10000,
            mission: get(container, 'div:nth-child(8) > dd'),
            Mission_type: /MT/.test(get(container, 'div:nth-child(8) > dd')) ? 'MT' :
              /AT|CVT/.test(get(container, 'div:nth-child(8) > dd')) ? 'AT/CVT' : '指定なし',
            model: '指定なし',
            model_year: modelYear,
            price: priceStr,
            price_range: (rawPrice === '応談' || priceStr === '0') ? '応談' : null, // 後で計算
            price_range_500: (rawPrice === '応談' || priceStr === '0') ? '応談' : null, // 後で計算
            price_range_1000: (rawPrice === '応談' || priceStr === '0') ? '応談' : null, // 後で計算
            region: '日本（カーセンサー）',
            source: 'カーセンサー',
            sourceURL: container.querySelector('h3 > a')?.href || null,
            vehicle_inspection: get(container, 'div:nth-child(3) > dd'),
            driving_system: '指定なし',
            engine_type: '指定なし',
            handle: '指定なし',
            safe_equipment: 'なし',
            comfort_equipment: 'なし',
            interia_equipment: 'なし',
            exteria_equipment: 'なし'
          };
        });
      });

      // price_rangeの計算
      for (const car of cars) {
        const priceVal = parseFloat(car.price);
        if (car.price_range !== '応談') {
          car.price_range = getPriceRange100(priceVal);
        }
        if (car.price_range_500 !== '応談') {
          car.price_range_500 = getPriceRange500(priceVal);
        }
        if (car.price_range_1000 !== '応談') {
          car.price_range_1000 = getPriceRange1000(priceVal);
        }
      }

      // 詳細取得＆上書き
      for (const car of cars) {
        if (!car.sourceURL) continue;
        const detailPage = await context.newPage();
        const details = await getDetails(detailPage, car.sourceURL);
        if (details) {
          car.driving_system = details.driving_system;
          car.engine_type = details.engine_type;
          car.handle = details.handle;
          car.model = details.modelText;
          car.safe_equipment = details.safeequipment;
          car.comfort_equipment = details.comfortequipment;
          car.interia_equipment = details.interiaequipment;
          car.exteria_equipment = details.exteriaequipment;
          car.image = (details.images || []).slice(0, 10).join(','); // 詳細ページの画像URL
        }
        await detailPage.close();
        await page.waitForTimeout(200 + Math.random() * 300);
      }


      // // CSV 出力
      // const header = [
      //   'car_name','detail','displacement','driving_system','engine_type','fix_history','handle',
      //   'image','insurance','maintenance','maker','mileage','mission','Mission_type','model','model_year',
      //   'price','price_range','price_range_500','price_range_1000',
      //   'region','source','sourceURL','vehicle_inspection',
      //   'safe_equipment','comfort_equipment','interia_equipment','exteria_equipment'
      // ];

      // // ここで再度画像を10枚までに制限
      // cars.forEach(car => {
      //   if (car.image) {
      //     car.image = car.image.split(',').filter(Boolean).slice(0, 10).join(',');
      //   }
      // });

      // const csvBody = cars.map(c =>
      //   header.map(h => {
      //     const v = c[h] ?? '';
      //     return /[",\n]/.test(v) ? `"${v.replace(/"/g, '""')}"` : v;
      //   }).join(',')
      // ).join('\n');

      // // 日本時間タイムスタンプ
      // const jst = new Date(Date.now() + 9 * 3600 * 1000);
      // const pad = n => n.toString().padStart(2, '0');
      // const Y = jst.getUTCFullYear();
      // const M = pad(jst.getUTCMonth() + 1);
      // const D = pad(jst.getUTCDate());
      // const h = pad(jst.getUTCHours());
      // const m = pad(jst.getUTCMinutes());
      // const s = pad(jst.getUTCSeconds());
      // const dateString = `${Y}-${M}-${D}-${h}-${m}-${s}`;

      // const filename = path.join(outputDir, `${brandName}_${dateString}.csv`);
      // fs.writeFileSync(filename, '\uFEFF' + header.join(',') + '\n' + csvBody);
      // console.log(`✏️  ${filename} に ${cars.length} 件を書き出し`);

      // carsをallCarsに追加
      allCars.push(...cars);

      // Save to database per brand
      if (cars.length > 0) {
        await outputManager.saveData(cars, brandName);
        console.log(`   💾 Saved ${cars.length} cars to database for ${brandName}`);
      }

      // 次ページへ
      const nextBtn = await page.$('#js-resultBar .pager__btn__next:not(.is-disabled)');
      if (!nextBtn) break;
      await Promise.all([
        nextBtn.click(),
        page.waitForLoadState('domcontentloaded')
      ]);
      pageCount++;
      console.log(`➡️  ${brandName} ページ ${pageCount} を読み込み`);
      await page.waitForTimeout(500 + Math.random() * 500);
    }
  }
  // 画像を最大10枚に制限
  allCars.forEach(car => {
    if (car.image) {
      car.image = car.image.split(',').filter(Boolean).slice(0, 10).join(',');
    }
  });

  // ヘッダーとCSVボディの定義
  const header = [
    'car_name', 'detail', 'displacement', 'driving_system', 'engine_type', 'fix_history', 'handle',
    'image', 'insurance', 'maintenance', 'maker', 'mileage', 'mission', 'Mission_type', 'model', 'model_year',
    'price', 'price_range', 'price_range_500', 'price_range_1000',
    'region', 'source', 'sourceURL', 'vehicle_inspection',
    'safe_equipment', 'comfort_equipment', 'interia_equipment', 'exteria_equipment'
  ];

  const csvBody = allCars.map(c =>
    header.map(h => {
      const v = c[h] ?? '';
      // カンマや改行、ダブルクォートが含まれる場合はダブルクォートで囲む
      return /[",\n]/.test(v) ? `"${v.replace(/"/g, '""')}"` : v;
    }).join(',')
  ).join('\n');

  // CSV書き出し
  const jst = new Date(Date.now() + 9 * 3600 * 1000);
  const pad = n => n.toString().padStart(2, '0');
  const Y = jst.getUTCFullYear();
  const M = pad(jst.getUTCMonth() + 1);
  const D = pad(jst.getUTCDate());
  const h = pad(jst.getUTCHours());
  const m = pad(jst.getUTCMinutes());
  const s = pad(jst.getUTCSeconds());
  const dateString = `${Y}-${M}-${D}-${h}-${m}-${s}`;
  const filename = path.join(outputDir, `all_brands_${dateString}.csv`);
  fs.writeFileSync(filename, '\uFEFF' + header.join(',') + '\n' + csvBody);
  console.log(`✏️  ${filename} に全ブランド分 ${allCars.length} 件を書き出し`);

  await browser.close();
})();
