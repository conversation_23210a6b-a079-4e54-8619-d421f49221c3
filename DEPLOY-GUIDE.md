# 中古車販売システム - 本番環境デプロイガイド

このガイドでは、Dockerを使用してUbuntuサーバーにNuxt.js中古車販売アプリケーションを本番環境にデプロイする手順を詳しく説明いたします。

## 前提条件

以下の環境が準備されていることを確認してください：

- **サーバー環境**: Ubuntu 22.04 LTS以降のVPS（さくらのVPSを推奨）
- **アクセス権限**: root権限またはsudo権限を持つアカウント
- **ドメイン**: サーバーIPアドレスに向けて設定済みのドメイン名
- **最小システム要件**: 
  - メモリ: 4GB以上（推奨：8GB）
  - CPU: 2コア以上
  - ストレージ: 40GB以上の空き容量

⚠️ **注意**: メモリが4GB未満の場合、アプリケーションが正常に動作しない可能性があります。

## 0. サーバーへのSSH接続

このガイドを開始する前に、サーバーにSSH接続できることを確認してください。

**さくらのVPSでのUbuntu接続方法**: https://manual.sakura.ad.jp/vps/support/technical/ubuntu-login.html

- **ユーザー名**: `ubuntu`
- **パスワード**: OS標準インストール時に設定したパスワード
- **接続方法**: コントロールパネルからサーバーのグローバルIPアドレスを確認してSSH接続

```bash
# SSH接続例（IPアドレスは実際のものに置き換え）
ssh ubuntu@your-server-ip
```

**期待される結果**: SSH接続が成功すると、以下のような画面が表示されます：

![SSH Login Success](https://pub-f4bbfc272acd40afbbcfb83799e1865d.r2.dev/usedcar/CleanShot%202025-09-02%20at%2010.32.33%402x.png)

ここで`ubuntu@hostname:~$`のプロンプトが表示されていれば、正常にログインできています。

## 1. サーバーの初期設定

### 1.1 システムの更新とDockerのインストール

まず、システムを最新の状態に更新し、Dockerをインストールします。この作業には約5-10分程度かかります。

**Docker公式インストール手順**: https://qiita.com/kujiraza/items/00b9066c49ddfc718fd6#docker-%E3%81%AE%E3%83%AA%E3%83%9D%E3%82%B8%E3%83%88%E3%83%AA%E3%81%8B%E3%82%89%E3%82%A4%E3%83%B3%E3%82%B9%E3%83%88%E3%83%BC%E3%83%AB

```bash
# システムパッケージを最新に更新
sudo apt update && sudo apt upgrade -y

# 基本ソフトウェアのインストール
sudo apt install -y unzip nginx certbot python3-certbot-nginx

# 古いDockerバージョンの削除（念のため）
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do 
    sudo apt-get remove $pkg 2>/dev/null || true
done

# Docker公式インストール用の依存関係
sudo apt install -y ca-certificates curl

# Docker公式リポジトリの設定
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Docker公式版のインストール
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Dockerサービスの開始と自動起動設定
sudo systemctl enable --now docker

# 現在のユーザーをdockerグループに追加（sudo無しでdockerコマンドを実行可能にする）
sudo usermod -aG docker $USER
```

**期待される結果**: エラーなくインストールが完了し、`docker --version`でバージョンが表示されること。

![Docker Installation Complete](https://pub-f4bbfc272acd40afbbcfb83799e1865d.r2.dev/usedcar/uc-docker-version.png)

### 1.2 ファイアウォールの設定

セキュリティを確保するため、必要なポートのみを開放します。

```bash
# SSH接続用ポート（22番）を許可
sudo ufw allow OpenSSH

# HTTP通信用ポート（80番）を許可
sudo ufw allow 80

# HTTPS通信用ポート（443番）を許可  
sudo ufw allow 443

# ファイアウォールを有効化
sudo ufw enable
```

**確認方法**: `sudo ufw status`で設定されたルールが表示されること。

![UFW Status](https://pub-f4bbfc272acd40afbbcfb83799e1865d.r2.dev/usedcar/ufw.png)

### 1.3 スワップファイルの作成（メモリ不足対策）

メモリ不足によるシステムクラッシュを防ぐため、スワップファイルを作成します。

```bash
# 1GBのスワップファイルを作成
sudo fallocate -l 1G /swapfile

# セキュリティのため、ファイルの権限を制限
sudo chmod 600 /swapfile

# スワップファイルとして設定
sudo mkswap /swapfile

# スワップファイルを有効化
sudo swapon /swapfile

# システム再起動時にも自動でマウントされるよう設定
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

**確認方法**: `free -h`コマンドでSwapの行に容量が表示されること。

![Swap Configuration](https://pub-f4bbfc272acd40afbbcfb83799e1865d.r2.dev/usedcar/swap%20free.png)

### 1.4 重要な注意事項

⚠️ **必須作業**: Dockerグループの権限を適用するため、**必ず一度ログアウトして再ログインしてください**。

```bash
# ログアウト
exit

# 再度SSH接続を行う
```

## 2. アプリケーションのデプロイ

### 2.1 アプリケーションファイルのアップロード

アプリケーションのZIPファイルをサーバーにアップロードします。

**手順**:
1. `usedcarsales-nuxt.zip`を`/home/<USER>/`ディレクトリにアップロード
2. アップロード方法の詳細については以下を参照: https://rental-server.blog/ftp-sftp-sakura-rentalserver/

⚠️ **注意**: ファイルサイズが大きい場合、アップロードに時間がかかる場合があります。

### 2.2 ファイルの展開とディレクトリ移動

```bash
# ホームディレクトリに移動
cd ~/

# ZIPファイルを展開（時間がかかる場合があります）
unzip usedcarsales-nuxt.zip

# アプリケーションディレクトリに移動
cd usedcarsales-nuxt
```

**期待される結果**: `ls`コマンドで`docker-compose.prod.yml`や`.env.example`などのファイルが確認できること。

### 2.3 環境設定ファイルの準備

#### 2.3.1 設定ファイルの作成

```bash
# サンプル設定ファイルをコピーして本番用設定を作成
cp .env.example .env
```

#### 2.3.2 Directus用暗号化キーの生成

セキュリティを確保するため、Directus用の暗号化キーを生成します。

```bash
# DIRECTUS_KEY用の32文字のランダム文字列を生成
echo "DIRECTUS_KEY用:"
openssl rand -hex 32

# DIRECTUS_SECRET用の64文字のランダム文字列を生成  
echo "DIRECTUS_SECRET用:"
openssl rand -hex 64
```

⚠️ **重要**: 生成された値を必ずメモしてください。これらの値は後で`.env`ファイルに設定します。

#### 2.3.3 環境設定ファイルの編集

```bash
# 設定ファイルを編集
nano .env
```

以下の内容で設定してください（`your-domain.com`は実際のドメインに置き換え）:

```bash
# ===== 基本設定 =====
NODE_ENV=production
APP_PORT=3001
EXTERNAL_HOST=your-domain.com

# ===== データベース設定 =====
# 以下はDockerコンテナ間通信の設定のため、変更不要
DB_HOST=cockroach_db
DB_PORT=26257
DB_NAME=usedcarsales
DB_USER=root
DB_PASSWORD=
DB_SSL=false

# ===== Directus管理画面設定 =====
# 上記で生成した値を設定
DIRECTUS_KEY=上記で生成した32文字の値をここに入力
DIRECTUS_SECRET=上記で生成した64文字の値をここに入力
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=安全なパスワードを設定
DIRECTUS_PUBLIC_URL=https://yourdomain.com/directus

# ===== スクレイパー設定（オプション） =====
OUTPUT_MODE=database
BATCH_SIZE=100
```

**設定完了後**: `Ctrl + X`、`Y`、`Enter`の順に押してファイルを保存。

### 2.4 Dockerコンテナの起動

すべてのサービス（データベース、管理画面、アプリケーション）を起動します。初回起動時はアプリケーションのビルドも行うため約5分程度かかります。

```bash
# 本番環境用のDocker Composeファイルでサービスをビルド・起動
# この処理は約5分程度かかります
# 途中で止まったように見える場合がありますが、完了するまでお待ちください
docker compose -f docker-compose.prod.yml up -d --build
```

**起動確認**:
```bash
# コンテナの状態を確認
docker compose -f docker-compose.prod.yml ps
```

**期待される結果**: すべてのコンテナが`Up`または`healthy`状態になること。

![Build Complete and Services Running](https://pub-f4bbfc272acd40afbbcfb83799e1865d.r2.dev/usedcar/built%20finish%20and%20running.png)

## 3. Nginxリバースプロキシの設定

### 3.1 Nginx設定ファイルの作成

Webサーバーとしてのアクセスを可能にするため、Nginxの設定を行います。

```bash
# 設定ファイルを作成（your-domain.comは実際のドメインに置き換え）
sudo nano /etc/nginx/sites-available/usedcar-app
```

以下の内容を設定してください:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Directus管理画面とそのアセット（/directusパスでアクセス可能）
    location /directus/ {
        proxy_pass http://127.0.0.1:8056/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Redirect /directus to /directus/
    location = /directus {
        return 301 /directus/;
    }

    # メインのNuxtアプリケーション（ポート3001に転送）
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3.2 Nginx設定の有効化

```bash
# デフォルトサイトを無効化（競合を避けるため）
sudo rm -f /etc/nginx/sites-enabled/default

# 作成した設定を有効化
sudo ln -s /etc/nginx/sites-available/usedcar-app /etc/nginx/sites-enabled/

# 設定ファイルの文法チェック
sudo nginx -t

# Nginxサービスを再読み込み
sudo systemctl reload nginx
```

**期待される結果**: `nginx -t`で「syntax is ok」と「test is successful」が表示されること。

## 4. DNS設定

SSL証明書の取得前に、ドメインがサーバーを正しく指すよう設定する必要があります。

**詳細な手順については**: https://manual.sakura.ad.jp/cloud/appliance/dns/index.html

### 4.1 DNSゾーンの作成

**さくらのクラウドコントロールパネルでの操作**:

1. **DNSサービスにアクセス**
   - コントロールパネルにログイン
   - 左側メニューから「DNS」をクリック

2. **新しいゾーンの作成**
   - 「追加」ボタンをクリック
   - ドメイン名を入力（例：`your-domain.com`）
   - 説明欄に「中古車販売システム」などの識別しやすい名称を入力
   - 「作成」をクリック

### 4.2 Aレコードの設定

**リソースレコードの追加**:

1. **作成したDNSゾーンを選択**
2. **「リソースレコード」タブをクリック**
3. **「追加」ボタンをクリック**
4. **以下の内容で設定**:
   ```
   名前: @ （ルートドメインの場合）
   タイプ: A
   値: your-server-ip-address （実際のサーバーIPアドレス）
   TTL: 3600 （デフォルト値）
   ```
5. **「追加」をクリック**
6. **⚠️重要: 「反映」ボタンをクリック** （この手順を忘れると設定が適用されません）

### 4.3 DNS伝播の確認

DNS設定の反映には時間がかかります（通常30分〜2時間）。

```bash
# DNS解決の確認（your-domain.comは実際のドメインに置き換え）
nslookup your-domain.com
```

**期待される結果**: サーバーのIPアドレスが返されること。

**もし正しくない場合**: 時間をおいて再度確認してください。DNS伝播は地域により時間差があります。

### 4.4 HTTP接続の事前確認

SSL証明書取得前に、HTTP接続が正常に動作することを確認します。

```bash
# HTTP接続のテスト
curl -I http://your-domain.com
```

**期待される結果**: `HTTP/1.1 200 OK`が返されること。

## 5. SSL証明書の設定

### 5.1 Let's Encrypt証明書の取得

HTTPS通信を有効にするため、無料のSSL証明書を取得・設定します。

```bash
# SSL証明書の自動取得・設定（your-domain.comは実際のドメインに置き換え）
sudo certbot --nginx -d your-domain.com
```

**対話形式での入力内容**:
1. メールアドレスの入力: 証明書の期限切れ通知用
2. 利用規約への同意: `Y`を入力
3. メール配信の可否: `N`を推奨（不要な場合）

**期待される結果**: 「Congratulations!」メッセージが表示され、証明書が正常に設定されること。

### 5.2 自動更新の確認

Let's Encrypt証明書は90日間有効です。自動更新が設定されているか確認します。

```bash
# 自動更新のテスト
sudo certbot renew --dry-run
```

**期待される結果**: エラーなく「dry run successful」が表示されること。

## 6. デプロイ完了確認

### 6.1 サービス状態の確認

```bash
# Dockerコンテナの状態確認
docker compose -f docker-compose.prod.yml ps
```

**期待される結果**: すべてのコンテナが`healthy`または`Up`状態。

### 6.2 Webサイトへのアクセステスト

**以下のURLにブラウザでアクセスして動作確認**:

1. **メインアプリケーション**: `https://your-domain.com`
   - 期待結果: 中古車販売システムのトップページが表示
   
2. **管理画面**: `https://your-domain.com/directus`
   - 期待結果: Directusログイン画面が表示
   - ログイン情報: `.env`で設定したメールアドレス・パスワード

⚠️ **トラブルシューティング**: もしアクセスできない場合は、「8. トラブルシューティング」を参照してください。

## 7. 運用・メンテナンス

### 7.1 ログの確認方法

システムの動作状況やエラーの確認に使用します。

```bash
# Nuxtアプリケーションのログ確認
docker compose -f docker-compose.prod.yml logs -f nuxt-app

# Directus管理画面のログ確認  
docker compose -f docker-compose.prod.yml logs -f directus

# データベースのログ確認
docker compose -f docker-compose.prod.yml logs -f cockroach_db
```

### 7.2 アプリケーションの更新手順

新しいバージョンのアプリケーションをデプロイする場合の手順：

```bash
# 1. 新バージョンのZIPファイルを~/にアップロード

# 2. 現在のアプリケーションを停止
cd ~/usedcarsales-nuxt
docker compose -f docker-compose.prod.yml down

# 3. バックアップの作成（推奨）
cp -r usedcarsales-nuxt usedcarsales-nuxt-backup-$(date +%Y%m%d)

# 4. 新バージョンを展開（既存ファイルを上書き）
cd ~/
unzip -o usedcarsales-nuxt-new.zip
cd usedcarsales-nuxt

# 5. アプリケーションを再ビルド・起動
docker compose -f docker-compose.prod.yml up -d --build
```

### 7.3 データバックアップ

重要なデータの定期バックアップを推奨します。

```bash
# データベースのバックアップ作成
docker run --rm -v usedcarsales-nuxt_cockroach_data:/data -v $(pwd):/backup busybox tar czf /backup/db-backup-$(date +%Y%m%d).tar.gz /data

# バックアップファイルの確認
ls -la db-backup-*.tar.gz
```

**推奨頻度**: 毎週1回以上、重要なデータ更新後は随時。

## 8. トラブルシューティング

### 8.1 サービスが起動しない場合

**症状**: `docker compose ps`でコンテナが`Exited`状態

**対処方法**:
```bash
# 詳細なエラーログを確認
docker compose -f docker-compose.prod.yml logs

# 特定のサービスのログを確認
docker compose -f docker-compose.prod.yml logs [サービス名]
```

**一般的な原因**:
- `.env`ファイルの設定ミス
- メモリ不足
- ポートの競合

### 8.2 データベース接続エラー

**症状**: アプリケーションでデータベースエラーが発生

**確認事項**:
- `.env`ファイルで`DB_HOST=cockroach_db`が正しく設定されているか
- すべてのコンテナが同じDockerネットワークに接続されているか

**対処方法**:
```bash
# コンテナのネットワーク確認
docker network ls
docker network inspect usedcarsales-nuxt_usedcar_network
```

### 8.3 SSL証明書の問題

**症状**: `certbot`でエラーが発生、またはHTTPS接続できない

**確認事項**:
1. **DNS設定の確認**
   ```bash
   nslookup your-domain.com
   dig your-domain.com
   ```

2. **ファイアウォール設定の確認**
   ```bash
   sudo ufw status
   ```

3. **Nginx設定の確認**
   ```bash
   sudo nginx -t
   ```

**一般的な対処方法**:
- DNS伝播を待つ（最大24時間）
- ファイアウォールでポート80/443が開放されているか確認
- 他のWebサーバーが同じポートを使用していないか確認

### 8.4 メモリ不足の問題

**症状**: コンテナが突然停止する、システムが遅い

**確認方法**:
```bash
# メモリ使用状況の確認
free -h
top
```

**対処方法**:
- スワップファイルは初期設定で作成済み
- 必要に応じてサーバースペックの増強を検討

### 8.5 アクセスできない場合のチェックリスト

**以下を順番に確認してください**:

1. ✅ DNSが正しく設定されているか (`nslookup your-domain.com`)
2. ✅ ファイアウォールで必要なポートが開放されているか (`sudo ufw status`)
3. ✅ Nginxが正常に動作しているか (`sudo systemctl status nginx`)
4. ✅ Dockerコンテナが全て起動しているか (`docker compose ps`)
5. ✅ SSL証明書が正常に設定されているか (`sudo certbot certificates`)

## 9. 本番環境に関する重要事項

### 9.1 システム構成について

- **Dockerイメージ**: アプリケーションはローカルでビルドされ、その他のサービスは固定バージョンのイメージを使用
- **データ永続化**: すべてのデータはDockerボリュームに保存され、コンテナ再起動時も保持
- **プロキシ**: NginxがSSL終端処理とリバースプロキシを担当
- **自動処理**: スクレイパーは設定されたスケジュール（nuxt.config.ts）で自動実行

### 9.2 セキュリティに関する注意事項

- **定期更新**: システムパッケージの定期更新を推奨
- **パスワード管理**: 強固なパスワードの設定と定期変更
- **バックアップ**: 重要なデータの定期バックアップ
- **ログ監視**: 異常なアクセスやエラーの定期確認

### 9.3 サポート・問い合わせ

**技術的な問題が発生した場合**:
1. まず本ガイドのトラブルシューティングを確認
2. ログファイルでエラーの詳細を確認
3. 必要に応じて開発チームに問い合わせ

**本デプロイガイドの改善提案やご質問**:
開発チームまでお気軽にお問い合わせください。

---

**🎉 デプロイ完了おめでとうございます！**

本ガイドに従って設定が完了すれば、中古車販売システムが本番環境で正常に動作するはずです。何かご不明な点がございましたら、お気軽にお問い合わせください。