# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start Nuxt development server (main development command)
- `npm run build` - Build for production  
- `npm run preview` - Preview production build
- `npm run generate` - Generate static site

### Database Operations
- `npm run check-db` - Check database connectivity and get overview stats (runs `node directus/scripts/check-database.mjs`)
- `npm run generate-sample-data` - Generate 1 million sample car records (runs `node directus/scripts/generate-sample-data.mjs`)
- `npm run generate-test-data` - Generate 10,000 test records (runs `node directus/scripts/generate-sample-data.mjs 10000 100`)
- `npm run stress-test-db` - Run database performance tests (runs `node directus/scripts/stress-test-database.mjs`)
- `npm run stress-test-db-heavy` - Run intensive stress tests with higher concurrency

### Docker Operations
- `npm run docker:up` / `docker-compose up` - Start database and Directus services only (Nuxt app runs locally)
- `npm run docker:down` / `docker-compose down` - Stop all services
- `npm run docker:logs` - View Nuxt app logs
- `npm run docker:build` - Rebuild containers
- `docker-compose -f docker-compose.prod.yml up -d` - Production deployment with pre-built images

### Scraper Operations
- `OUTPUT_MODE=database node scraper/carsensor.js` - Run scraper with DB output
- `OUTPUT_MODE=csv node scraper/carsensor.js` - Run scraper with CSV output
- `OUTPUT_MODE=both node scraper/carsensor.js` - Run scraper with both outputs

## Architecture Overview

This is a **used car sales management platform** built with Nuxt 3, featuring web scraping capabilities and comprehensive data management. The system is designed for Japanese car market data aggregation and analysis.

### Technology Stack
- **Frontend**: Nuxt 3 with Nuxt UI Pro, TypeScript, and auto-imports
- **Database**: CockroachDB (PostgreSQL-compatible) with connection pooling
- **Admin Interface**: Directus headless CMS for data management
- **Scraping Engine**: Playwright-based scrapers with retry logic and email alerts
- **Containerization**: Docker with multi-service orchestration
- **Caching**: Redis for Directus performance optimization
- **Styling**: TailwindCSS with custom theme configuration

### Core Services Architecture

1. **Nuxt Application** (Port 3000)
   - Full-stack web interface with TypeScript and Nuxt UI Pro
   - Pages: Dashboard, Vehicle Management, Settings, Login, Debug
   - Server-side API endpoints (`/server/api/`)
   - Scheduled tasks via Nitro (`/server/tasks/`)
   - Database utilities and connection management
   - Authentication middleware and composables

2. **CockroachDB** (Port 26257, UI: 8080/8081)
   - Primary data store for car listings
   - Health monitoring and statistics
   - Automatic schema initialization
   - Supports connection pooling and performance optimization

3. **Directus** (Port 8056)
   - Headless CMS for data management
   - REST API for external integrations (Bubble.io compatible)
   - Redis caching integration
   - CORS enabled for cross-origin access from Nuxt frontend

4. **Scraper System**
   - Independent Node.js scrapers in `/scraper/` directory
   - Configurable output modes (CSV, database, both)
   - Blacklist filtering and duplicate detection
   - Comprehensive error handling and retry logic

### Key Data Flow

1. **Scraping Process**: CarSensor → Playwright scraper → Data processing → CockroachDB/CSV
2. **Scheduling**: Nuxt scheduled tasks (`scraper:run`) run weekly via cron
3. **API Access**: Directus provides REST API for external systems
4. **Monitoring**: Health checks, database stats, and scraper status tracking

### Frontend Application Structure

The Nuxt application follows a modern full-stack architecture:

- **Pages**: `/app/pages/` - Route-based pages (index, login, cars, settings, debug)
- **Components**: `/app/components/` - Reusable Vue components (TestTable, etc.)
- **Layouts**: `/app/layouts/` - Page layout templates
- **Composables**: `/app/composables/` - Reusable Vue composition functions (useDirectus, useUser)
- **Middleware**: `/app/middleware/` - Route middleware (auth.global.ts)
- **Shared**: `/shared/utils/` - Cross-platform utilities (directus.ts)

### Environment Configuration

The system uses runtime configuration in `nuxt.config.ts` with environment variable mapping:

- **Database**: `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `DB_SSL`
- **Scraper**: `OUTPUT_MODE`, `BATCH_SIZE`, `SCRAPER_SCHEDULE_CRON`
- **Blacklist**: `BLACKLIST_ENABLED`, `BLACKLIST_CACHE_EXPIRY`, `BLACKLIST_FAIL_OPEN`
- **Directus Integration**: `DIRECTUS_URL`, `DIRECTUS_PORT` (publicly exposed)
- **External Services**: `DB_UI_PORT`, `EXTERNAL_HOST`

### Key Configuration Features

- **Experimental Features**: Typed pages enabled for better TypeScript integration
- **UI Configuration**: Nuxt UI Pro with color mode disabled for consistent theming
- **CORS Setup**: Directus configured to accept requests from Nuxt frontend
- **Development Mode**: Current setup runs Nuxt locally with databases in Docker

### Database Schema Highlights

The main `used_cars` table contains:
- Basic car information (maker, model, year, price, mileage)
- Technical specifications (engine, transmission, drivetrain)
- Equipment arrays (safety, comfort, interior, exterior features as JSONB)
- Source tracking (URL-based deduplication)
- Comprehensive metadata and timestamps

### Scheduled Tasks System

Uses Nuxt Nitro's experimental tasks feature:
- `scraper:run` - Weekly car data scraping (configurable via `SCRAPER_SCHEDULE_CRON`)
- `db-health` - Database health monitoring
- Tasks are defined in `/server/tasks/` and managed via environment configuration

### API Endpoints Structure

- `/api/database/health` - Database health check
- `/api/database/stats` - Database statistics and overview
- `/api/scraper/start` - Manual scraper trigger
- `/api/scraper/status` - Scraper execution status
- `/api/directus/health` - Directus service health

### Development vs Production

- **Development**: Nuxt runs locally (`npm run dev`) with databases in Docker containers
- **Production**: Pre-built Docker images from GitHub registry (`docker-compose.prod.yml`)
- Configuration managed via `.env` files and docker-compose variants
- Deployment guide available in `DEPLOY.md`

### External Integrations

- **Directus REST API**: Full CRUD operations for external systems
- **Authentication**: Token-based API access for third-party integration
- **CORS Configuration**: Configured for Nuxt frontend cross-origin requests
- **Documentation**: Comprehensive API guides in `/docs/` directory (English and Japanese)

### Important Development Notes

- The current Docker setup only runs **database and Directus services** - the Nuxt app runs locally for development
- Use `npm run dev` for normal development with hot-reload
- Use `docker-compose up` to start supporting services (CockroachDB, Directus, Redis)
- The frontend is built with Nuxt UI Pro providing enterprise-grade components
- TypeScript is fully configured with experimental typed pages for better DX

This architecture supports high-volume data processing, scheduled operations, and provides both programmatic and UI-based data access for comprehensive used car market analysis.